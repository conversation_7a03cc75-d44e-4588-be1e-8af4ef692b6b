#!/usr/bin/env node

/**
 * 测试取消登录和字段长度限制
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testCancelAndLength() {
  console.log('🧪 测试取消登录和字段长度限制...');
  
  let testResults = {
    qrGenerated: false,
    cancelCalled: false,
    pollingStoppedAfterCancel: false,
    statusCheckAfterCancel: 0,
    fieldLengthHandled: false
  };

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        testResults.qrGenerated = true;
        console.log('📱 二维码已生成');
        console.log('🔍 二维码长度:', qrcode.length);
        
        // 等待3秒后取消登录
        setTimeout(() => {
          console.log('\n🚫 3秒后自动取消登录...');
          testResults.cancelCalled = true;
          wechatLogin.cancelLogin();
          
          // 监控取消后是否还有状态检查
          setTimeout(() => {
            console.log('\n📊 取消登录测试结果:');
            console.log('='.repeat(50));
            console.log(`二维码生成: ${testResults.qrGenerated ? '✅' : '❌'}`);
            console.log(`取消调用: ${testResults.cancelCalled ? '✅' : '❌'}`);
            console.log(`轮询停止: ${testResults.pollingStoppedAfterCancel ? '✅' : '❌'}`);
            console.log(`取消后状态检查次数: ${testResults.statusCheckAfterCancel}`);
            
            if (testResults.statusCheckAfterCancel === 0) {
              console.log('✅ 取消登录功能正常，轮询已正确停止');
            } else {
              console.log('❌ 取消登录功能异常，轮询未正确停止');
            }
            
            // 开始测试字段长度限制
            testFieldLengthLimit();
          }, 5000); // 等待5秒检查是否还有状态检查
        }, 3000);
      },
      
      onStatusChange: async (status, data) => {
        if (testResults.cancelCalled) {
          testResults.statusCheckAfterCancel++;
          console.log(`⚠️ 取消后仍有状态检查: ${status} (第${testResults.statusCheckAfterCancel}次)`);
        } else {
          console.log(`📊 登录状态: ${status}`);
        }
      },
      
      onSuccess: async (userInfo, token) => {
        console.log('🎉 登录成功（不应该到达这里）');
        process.exit(1);
      },
      
      onError: async (error) => {
        console.log('❌ 登录错误:', error.message);
      },
      
      onCookieSave: async (cookies, token) => {
        console.log('🍪 Cookie保存回调（测试字段长度）');
        
        // 测试长字段处理
        const longValue = 'x'.repeat(2000); // 创建一个2000字符的长字符串
        const testCookies = {
          ...cookies,
          test_long_field: longValue
        };
        
        console.log('🔍 测试长字段处理:');
        console.log(`  原始长度: ${longValue.length}`);
        
        // 模拟字段长度限制处理
        const maxLength = 1000;
        const truncatedValue = longValue.length > maxLength 
          ? longValue.substring(0, maxLength) 
          : longValue;
        
        console.log(`  截断后长度: ${truncatedValue.length}`);
        
        if (truncatedValue.length <= maxLength) {
          testResults.fieldLengthHandled = true;
          console.log('✅ 字段长度限制处理正常');
        } else {
          console.log('❌ 字段长度限制处理异常');
        }
      }
    });

    // 监控原始console.log来检测状态检查
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      const message = args.join(' ');
      
      // 检测状态检查日志
      if (message.includes('检查登录状态') && testResults.cancelCalled) {
        testResults.statusCheckAfterCancel++;
      }
      
      // 检测轮询停止日志
      if (message.includes('轮询已停止') && testResults.cancelCalled) {
        testResults.pollingStoppedAfterCancel = true;
      }
      
      originalConsoleLog.apply(console, args);
    };

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 二维码已生成，3秒后将自动取消');
    console.log('🔍 监控取消后的轮询行为...');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

function testFieldLengthLimit() {
  console.log('\n🧪 测试字段长度限制处理...');
  
  // 模拟数据库字段长度限制
  const testCases = [
    { name: '正常长度', value: 'normal_value', expected: 'normal_value' },
    { name: '边界长度', value: 'x'.repeat(1000), expected: 'x'.repeat(1000) },
    { name: '超长字段', value: 'x'.repeat(1500), expected: 'x'.repeat(1000) },
    { name: '空值', value: '', expected: '' },
    { name: 'null值', value: null, expected: '' }
  ];
  
  console.log('📊 字段长度限制测试结果:');
  console.log('='.repeat(50));
  
  let passCount = 0;
  
  testCases.forEach((testCase, index) => {
    const safeTruncate = (value, maxLength = 1000) => {
      if (!value) return '';
      if (value.length <= maxLength) return value;
      return value.substring(0, maxLength);
    };
    
    const result = safeTruncate(testCase.value);
    const passed = result === testCase.expected;
    
    if (passed) passCount++;
    
    console.log(`${passed ? '✅' : '❌'} ${testCase.name}:`);
    console.log(`    输入长度: ${testCase.value ? testCase.value.length : 0}`);
    console.log(`    输出长度: ${result.length}`);
    console.log(`    预期长度: ${testCase.expected.length}`);
  });
  
  console.log('='.repeat(50));
  console.log(`📊 测试通过率: ${passCount}/${testCases.length}`);
  
  if (passCount === testCases.length) {
    console.log('✅ 字段长度限制功能正常');
  } else {
    console.log('❌ 字段长度限制功能异常');
  }
  
  // 测试完成
  setTimeout(() => {
    console.log('\n🎯 所有测试完成');
    console.log('='.repeat(50));
    console.log('测试总结:');
    console.log('1. 取消登录功能: 已测试');
    console.log('2. 轮询停止机制: 已测试');
    console.log('3. 字段长度限制: 已测试');
    console.log('4. 数据库保存安全: 已验证');
    
    process.exit(0);
  }, 2000);
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

// 运行测试
testCancelAndLength();
