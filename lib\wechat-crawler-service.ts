import { prisma } from './prisma';
import { WechatCrawlerLogin } from './wechat-crawler-login';
import { WebhookNotificationService, ArticleInfo } from './webhook-notification';

/**
 * 微信公众号爬取核心服务
 * 提供统一的爬取逻辑，供调度器和API端点使用
 */
export class WechatCrawlerService {
  private static instance: WechatCrawlerService;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 5;

  private constructor() {}

  public static getInstance(): WechatCrawlerService {
    if (!WechatCrawlerService.instance) {
      WechatCrawlerService.instance = new WechatCrawlerService();
    }
    return WechatCrawlerService.instance;
  }

  /**
   * 检查当前是否在允许执行的时间范围内（北京时间6:00-23:00）
   */
  public isInAllowedTimeRange(): boolean {
    const now = new Date();
    // 转换为北京时间（UTC+8）
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const hour = beijingTime.getUTCHours();

    // 允许执行时间：6:00-23:00（北京时间）
    const isAllowed = hour >= 6 && hour < 23;

    if (!isAllowed) {
      console.log(`⏰ 当前北京时间 ${hour}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}，不在允许执行时间范围内（6:00-23:00），跳过执行`);
    }

    return isAllowed;
  }

  /**
   * 获取需要爬取的公众号列表
   */
  private async getAccountsToCrawl(): Promise<any[]> {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    // 获取所有启用爬取的公众号
    const allEnabledAccounts = await prisma.wechatAccount.findMany({
      where: {
        enableCrawling: true,
        fakeid: { not: null }
      },
      include: {
        articles: {
          orderBy: {
            publishDate: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`📊 找到 ${allEnabledAccounts.length} 个启用爬取的公众号`);

    // 筛选需要爬取的账号（距离上次爬取超过30分钟）
    const accountsToCrawl = allEnabledAccounts.filter(account => {
      if (!account.lastCrawlTime) {
        return true; // 从未爬取过
      }
      
      const timeSinceLastCrawl = Date.now() - account.lastCrawlTime.getTime();
      const shouldCrawl = timeSinceLastCrawl >= 30 * 60 * 1000; // 30分钟
      
      if (!shouldCrawl) {
        const remainingMinutes = Math.ceil((30 * 60 * 1000 - timeSinceLastCrawl) / (60 * 1000));
        console.log(`⏳ ${account.name} 距离上次爬取不足30分钟，还需等待 ${remainingMinutes} 分钟`);
      }
      
      return shouldCrawl;
    });

    console.log(`🎯 筛选出 ${accountsToCrawl.length} 个需要爬取的公众号`);
    return accountsToCrawl;
  }

  /**
   * 执行单次爬取任务
   */
  public async executeCrawlTask(): Promise<{ success: boolean; message: string; stats?: any }> {
    console.log('🚀 开始执行文章爬取任务');
    
    try {
      // 检查时间范围
      if (!this.isInAllowedTimeRange()) {
        return {
          success: true,
          message: '不在执行时间范围内'
        };
      }

      // 获取需要爬取的公众号
      const accountsToCrawl = await this.getAccountsToCrawl();
      
      if (accountsToCrawl.length === 0) {
        console.log('📝 没有需要爬取的公众号');
        return {
          success: true,
          message: '没有需要爬取的公众号'
        };
      }

      // 获取爬虫登录实例
      const crawler = WechatCrawlerLogin.getInstance();
      
      let totalNewArticles = 0;
      let successfulAccounts = 0;
      let failedAccounts = 0;

      // 逐个处理公众号
      for (const account of accountsToCrawl) {
        try {
          console.log(`\n🔍 开始爬取公众号: ${account.name}`);
          
          // 更新爬取时间
          await prisma.wechatAccount.update({
            where: { id: account.id },
            data: { lastCrawlTime: new Date() }
          });

          // 执行爬取
          const result = await crawler.crawlArticles(account.fakeid, account.id);
          
          if (result.success) {
            totalNewArticles += result.newArticlesCount || 0;
            successfulAccounts++;
            console.log(`✅ ${account.name} 爬取完成，新增 ${result.newArticlesCount} 篇文章`);
          } else {
            failedAccounts++;
            console.error(`❌ ${account.name} 爬取失败: ${result.error}`);
          }

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          failedAccounts++;
          console.error(`❌ 爬取公众号 ${account.name} 时出错:`, error);
        }
      }

      // 重置连续失败计数
      if (successfulAccounts > 0) {
        this.consecutiveFailures = 0;
      } else {
        this.consecutiveFailures++;
      }

      const stats = {
        totalAccounts: accountsToCrawl.length,
        successfulAccounts,
        failedAccounts,
        totalNewArticles
      };

      console.log(`\n📊 爬取任务完成统计:`);
      console.log(`  - 处理公众号: ${stats.totalAccounts} 个`);
      console.log(`  - 成功: ${stats.successfulAccounts} 个`);
      console.log(`  - 失败: ${stats.failedAccounts} 个`);
      console.log(`  - 新增文章: ${stats.totalNewArticles} 篇`);

      // 检查是否需要发送告警
      await this.checkAndSendAlert();

      return {
        success: true,
        message: '爬取任务执行完成',
        stats
      };

    } catch (error) {
      this.consecutiveFailures++;
      console.error('❌ 爬取任务执行失败:', error);
      
      // 检查是否需要发送告警
      await this.checkAndSendAlert();

      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 检查并发送告警通知
   */
  private async checkAndSendAlert(): Promise<void> {
    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
      console.log(`🚨 连续失败 ${this.consecutiveFailures} 次，发送告警通知`);
      
      try {
        // 获取管理员用户的webhook配置
        const adminUsers = await prisma.user.findMany({
          where: { role: 'ADMIN' },
          include: { webhooks: true }
        });

        const webhookService = new WebhookNotificationService();
        
        for (const admin of adminUsers) {
          for (const webhook of admin.webhooks) {
            if (webhook.isActive) {
              try {
                await webhookService.sendAlert(webhook, {
                  title: '🚨 微信公众号爬取服务告警',
                  message: `爬取服务已连续失败 ${this.consecutiveFailures} 次，请检查服务状态。`,
                  timestamp: new Date().toISOString(),
                  level: 'ERROR'
                });
                console.log(`✅ 告警通知已发送到管理员 ${admin.email}`);
              } catch (error) {
                console.error(`❌ 发送告警通知失败 (${admin.email}):`, error);
              }
            }
          }
        }
      } catch (error) {
        console.error('❌ 获取管理员信息失败:', error);
      }
    }
  }

  /**
   * 获取连续失败次数
   */
  public getConsecutiveFailures(): number {
    return this.consecutiveFailures;
  }

  /**
   * 重置连续失败计数
   */
  public resetConsecutiveFailures(): void {
    this.consecutiveFailures = 0;
  }
}
