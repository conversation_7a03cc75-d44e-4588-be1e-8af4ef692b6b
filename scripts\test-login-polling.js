#!/usr/bin/env node

/**
 * 测试登录轮询问题
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testLoginPolling() {
  console.log('🧪 测试登录轮询问题...');
  
  let statusHistory = [];
  let pollingStoppedCorrectly = false;
  let finalLoginStarted = false;
  let finalLoginCompleted = false;

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        statusHistory.push({ time: Date.now(), event: 'qr_generated' });
      },
      
      onStatusChange: async (status, data) => {
        const timestamp = Date.now();
        statusHistory.push({ time: timestamp, event: 'status_change', status });
        console.log(`📊 状态变化: ${status} (${new Date(timestamp).toISOString()})`);
        
        if (status === 'confirmed') {
          console.log('✅ 状态已确认，检查轮询是否停止...');
          
          // 等待一段时间，检查是否还有状态检查
          setTimeout(() => {
            const recentStatusChecks = statusHistory.filter(h => 
              h.event === 'status_check' && h.time > timestamp
            );
            
            if (recentStatusChecks.length === 0) {
              pollingStoppedCorrectly = true;
              console.log('✅ 轮询已正确停止');
            } else {
              console.log('❌ 轮询未正确停止，仍有', recentStatusChecks.length, '次状态检查');
            }
          }, 5000); // 等待5秒检查
        }
      },
      
      onSuccess: async (userInfo, token) => {
        finalLoginCompleted = true;
        statusHistory.push({ time: Date.now(), event: 'login_success' });
        console.log('🎉 登录成功！');
        console.log('👤 用户信息:', userInfo);
        console.log('🔑 Token:', token);
        
        // 分析整个流程
        setTimeout(() => {
          analyzeLoginFlow();
          process.exit(0);
        }, 2000);
      },
      
      onError: async (error) => {
        statusHistory.push({ time: Date.now(), event: 'error', error: error.message });
        console.error('❌ 登录失败:', error);
        
        setTimeout(() => {
          analyzeLoginFlow();
          process.exit(1);
        }, 1000);
      },
      
      onCookieSave: async (cookies, token) => {
        statusHistory.push({ time: Date.now(), event: 'cookie_save' });
        console.log('🍪 Cookie保存回调触发');
        
        // 检查关键字段
        const criticalFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
        let validCount = 0;
        
        console.log('🔍 Cookie字段检查:');
        criticalFields.forEach(field => {
          const hasValue = cookies[field] && cookies[field].length > 0;
          if (hasValue) validCount++;
          console.log(`  ${field}: ${hasValue ? '✅' : '❌'}`);
        });
        
        console.log(`🎯 关键字段完整度: ${validCount}/${criticalFields.length}`);
      }
    });

    // 监控状态检查调用
    const originalCheckLoginStatus = wechatLogin.checkLoginStatus;
    if (originalCheckLoginStatus) {
      wechatLogin.checkLoginStatus = async function() {
        statusHistory.push({ time: Date.now(), event: 'status_check' });
        console.log('🔍 执行状态检查...');
        return await originalCheckLoginStatus.call(this);
      };
    }

    // 监控最终登录调用
    const originalPerformFinalLogin = wechatLogin.performFinalLogin;
    if (originalPerformFinalLogin) {
      wechatLogin.performFinalLogin = async function() {
        finalLoginStarted = true;
        statusHistory.push({ time: Date.now(), event: 'final_login_start' });
        console.log('🚀 开始最终登录...');
        return await originalPerformFinalLogin.call(this);
      };
    }

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('🔍 监控轮询行为...');
    
    // 设置超时
    setTimeout(() => {
      console.log('\n⏰ 测试超时');
      wechatLogin.cancelLogin();
      
      setTimeout(() => {
        analyzeLoginFlow();
        process.exit(0);
      }, 1000);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }

  function analyzeLoginFlow() {
    console.log('\n📊 登录流程分析');
    console.log('='.repeat(60));
    
    const startTime = statusHistory[0]?.time || Date.now();
    
    console.log('🕐 时间线:');
    statusHistory.forEach((entry, index) => {
      const elapsed = entry.time - startTime;
      const timeStr = `+${Math.floor(elapsed / 1000)}s`;
      console.log(`  ${timeStr.padStart(6)} | ${entry.event.padEnd(20)} | ${entry.status || entry.error || ''}`);
    });
    
    console.log('\n📈 统计信息:');
    const eventCounts = {};
    statusHistory.forEach(entry => {
      eventCounts[entry.event] = (eventCounts[entry.event] || 0) + 1;
    });
    
    Object.entries(eventCounts).forEach(([event, count]) => {
      console.log(`  ${event.padEnd(20)}: ${count} 次`);
    });
    
    console.log('\n🎯 关键检查:');
    console.log(`  轮询正确停止: ${pollingStoppedCorrectly ? '✅' : '❌'}`);
    console.log(`  最终登录开始: ${finalLoginStarted ? '✅' : '❌'}`);
    console.log(`  最终登录完成: ${finalLoginCompleted ? '✅' : '❌'}`);
    
    // 检查confirmed状态后是否还有状态检查
    const confirmedIndex = statusHistory.findIndex(h => h.status === 'confirmed');
    if (confirmedIndex >= 0) {
      const statusChecksAfterConfirmed = statusHistory.slice(confirmedIndex + 1)
        .filter(h => h.event === 'status_check');
      
      console.log(`  confirmed后状态检查: ${statusChecksAfterConfirmed.length} 次 ${statusChecksAfterConfirmed.length === 0 ? '✅' : '❌'}`);
      
      if (statusChecksAfterConfirmed.length > 0) {
        console.log('    ⚠️ 这表明轮询没有正确停止');
        statusChecksAfterConfirmed.forEach((check, i) => {
          const elapsed = check.time - statusHistory[confirmedIndex].time;
          console.log(`      ${i + 1}. +${elapsed}ms 后仍在检查状态`);
        });
      }
    }
    
    console.log('\n💡 建议:');
    if (!pollingStoppedCorrectly) {
      console.log('  - 检查 clearInterval 是否正确执行');
      console.log('  - 确认状态更新时机是否正确');
    }
    
    if (finalLoginStarted && !finalLoginCompleted) {
      console.log('  - 检查最终登录流程是否有异常');
      console.log('  - 查看Cookie保存是否成功');
    }
    
    if (eventCounts.status_check > 20) {
      console.log('  - 状态检查次数过多，可能存在轮询问题');
    }
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

// 运行测试
testLoginPolling();
