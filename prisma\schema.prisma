generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  email         String         @unique
  name          String?
  avatar        String?
  createdAt     DateTime       @default(now())
  inviteCode    String?        @unique
  invitedBy     String?
  isActive      Boolean        @default(true)
  password      String
  updatedAt     DateTime       @updatedAt
  role          UserRole       @default(USER)
  // Webhook配置字段
  webhookUrl    String?        // Webhook URL
  webhookType   WebhookType?   // Webhook类型：企业微信/钉钉/飞书
  webhookEnabled Boolean       @default(false) // 是否启用webhook通知
  inviteRewards InviteReward[]
  orders        Order[]
  subscriptions Subscription[]
  inviter       User?          @relation("UserInvites", fields: [invitedBy], references: [id])
  invitees      User[]         @relation("UserInvites")
}

model WechatAccount {
  id            String         @id @default(cuid())
  name          String
  avatar        String
  description   String?        @db.Text
  openid        String?        @unique
  // 新增字段用于存储搜索API返回的信息
  fakeid        String?        @unique  // 微信公众号的fakeid
  nickname      String?                 // 公众号昵称
  alias         String?                 // 公众号别名
  roundHeadImg  String?                // 圆形头像URL
  serviceType   Int?                   // 服务类型
  signature     String?        @db.Text // 公众号签名
  verifyStatus  Int?                   // 认证状态
  // 定时任务相关字段
  enableCrawling Boolean        @default(false)  // 是否启用定时爬取
  lastCrawlTime  DateTime?              // 最后一次爬取时间
  crawlStatus    String?        @default("idle") // 爬取状态: idle, running, error
  crawlError     String?        @db.Text // 最后一次爬取错误信息
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  articles      Article[]
  subscriptions Subscription[]
}

model Subscription {
  id              String        @id @default(cuid())
  userId          String
  wechatAccountId String
  createdAt       DateTime      @default(now())
  isActive        Boolean       @default(true)
  updatedAt       DateTime      @updatedAt
  user            User          @relation(fields: [userId], references: [id])
  wechatAccount   WechatAccount @relation(fields: [wechatAccountId], references: [id])

  @@unique([userId, wechatAccountId])
}

model Article {
  id              String        @id @default(cuid())
  title           String
  summary         String?        @db.Text
  coverImage      String?
  createdAt       DateTime      @default(now())
  likeCount       Int?          @default(0)
  publishDate     DateTime
  readCount       Int?          @default(0)
  updatedAt       DateTime      @updatedAt
  url             String        @db.Text
  wechatAccountId String
  author          String?
  tags            String?  @db.Text
  wechatAccount   WechatAccount @relation(fields: [wechatAccountId], references: [id])

  @@index([wechatAccountId, publishDate])
  @@index([publishDate])
}

model InviteReward {
  id        String   @id @default(cuid())
  inviterId String
  inviteeId String
  orderId   String
  amount    Decimal  @db.Decimal(10, 2)
  status    String   @default("pending")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  inviter   User     @relation(fields: [inviterId], references: [id])

  @@index([inviterId, status])
}

model SystemConfig {
  id        String   @id @default(cuid())
  name      String   @unique
  value     String   @db.VarChar(500)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}

model Order {
  id        String   @id @default(cuid())
  userId    String
  amount    Decimal  @db.Decimal(10, 2)
  status    String   @default("pending")
  type      String
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId, status])
}

enum UserRole {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum WebhookType {
  WEWORK      // 企业微信
  DINGTALK    // 钉钉
  FEISHU      // 飞书
}
