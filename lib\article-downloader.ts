import { PrismaClient } from '@prisma/client';
import JS<PERSON><PERSON> from 'jszip';
import mime from 'mime-types';

const prisma = new PrismaClient();

export interface DownloadOptions {
  withCredentials?: boolean;
  timeout?: number; // 超时时间（秒）
  maxRetries?: number; // 最大重试次数
}

export interface DownloadResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  errorMessage?: string;
  proxyServerId?: string;
}

export class ArticleDownloader {
  private defaultOptions: DownloadOptions = {
    withCredentials: true,
    timeout: 30,
    maxRetries: 3
  };

  /**
   * 获取可用的代理服务器列表
   */
  private async getAvailableProxies(): Promise<any[]> {
    return await prisma.proxyServer.findMany({
      where: { isActive: true },
      orderBy: { priority: 'asc' }
    });
  }

  /**
   * 使用代理下载资源
   */
  private async downloadWithProxy(
    url: string, 
    proxy: string | undefined, 
    options: DownloadOptions = {}
  ): Promise<Response> {
    const { withCredentials = true, timeout = 30 } = { ...this.defaultOptions, ...options };
    
    const headers: Record<string, string> = {};
    
    if (withCredentials) {
      try {
        // 从环境变量或配置中获取微信凭据
        const credentials = await this.getWechatCredentials();
        if (credentials) {
          headers.cookie = `pass_ticket=${credentials.pass_ticket};wap_sid2=${credentials.wap_sid2}`;
        }
      } catch (e) {
        console.warn('获取微信凭据失败:', e);
      }
    }

    let targetURL = proxy 
      ? `${proxy}?url=${encodeURIComponent(url)}&headers=${encodeURIComponent(JSON.stringify(headers))}`
      : url;
    
    // 强制使用HTTPS
    targetURL = targetURL.replace(/^http:\/\//, 'https://');

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout * 1000);

    try {
      const response = await fetch(targetURL, {
        signal: controller.signal,
        headers: proxy ? {} : headers
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 获取微信凭据
   */
  private async getWechatCredentials(): Promise<any> {
    // 从系统配置中获取微信凭据
    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: ['wechat_token', 'wechat_cookie', 'wechat_data_ticket']
        }
      }
    });

    const credentials: any = {};
    configs.forEach(config => {
      switch (config.name) {
        case 'wechat_token':
          credentials.token = config.value;
          break;
        case 'wechat_cookie':
          credentials.cookie = config.value;
          break;
        case 'wechat_data_ticket':
          credentials.dataTicket = config.value;
          break;
      }
    });

    // 解析cookie中的pass_ticket和wap_sid2
    if (credentials.cookie) {
      const passTicketMatch = credentials.cookie.match(/pass_ticket=([^;]+)/);
      const wapSid2Match = credentials.cookie.match(/wap_sid2=([^;]+)/);
      
      if (passTicketMatch) credentials.pass_ticket = passTicketMatch[1];
      if (wapSid2Match) credentials.wap_sid2 = wapSid2Match[1];
    }

    return credentials;
  }

  /**
   * 下载单篇文章的HTML
   */
  public async downloadArticleHTML(
    articleUrl: string, 
    title?: string,
    options: DownloadOptions = {}
  ): Promise<{ html: string; size: number }> {
    const proxies = await this.getAvailableProxies();
    const { maxRetries = 3 } = { ...this.defaultOptions, ...options };
    
    let lastError: Error | null = null;
    
    // 尝试使用每个代理服务器
    for (const proxy of proxies) {
      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          console.log(`🔄 尝试下载文章 "${title}" (代理: ${proxy.name}, 尝试: ${attempt + 1}/${maxRetries})`);
          
          const response = await this.downloadWithProxy(articleUrl, proxy.url, options);
          const html = await response.text();
          
          // 验证HTML是否完整
          if (!this.validateArticleHTML(html, title)) {
            throw new Error('文章HTML不完整或已被删除');
          }
          
          const size = new Blob([html]).size;
          console.log(`✅ 文章 "${title}" 下载成功 (大小: ${size} 字节)`);
          
          return { html, size };
        } catch (error) {
          lastError = error as Error;
          console.warn(`❌ 下载失败 (代理: ${proxy.name}, 尝试: ${attempt + 1}): ${lastError.message}`);
          
          // 如果不是最后一次尝试，等待一段时间再重试
          if (attempt < maxRetries - 1) {
            await this.delay(1000 * (attempt + 1)); // 递增延迟
          }
        }
      }
    }
    
    // 如果所有代理都失败，尝试直连
    try {
      console.log(`🔄 尝试直连下载文章 "${title}"`);
      const response = await this.downloadWithProxy(articleUrl, undefined, options);
      const html = await response.text();
      
      if (!this.validateArticleHTML(html, title)) {
        throw new Error('文章HTML不完整或已被删除');
      }
      
      const size = new Blob([html]).size;
      console.log(`✅ 文章 "${title}" 直连下载成功 (大小: ${size} 字节)`);
      
      return { html, size };
    } catch (error) {
      lastError = error as Error;
    }
    
    throw new Error(`下载文章失败: ${lastError?.message || '未知错误'}`);
  }

  /**
   * 验证文章HTML是否完整
   */
  private validateArticleHTML(html: string, title?: string): boolean {
    try {
      // 检查是否包含文章内容区域
      const hasContent = html.includes('#js_content') || html.includes('js_content');
      
      // 检查是否是删除页面
      const isDeleted = html.includes('#js_fullscreen_layout_padding') && 
                       !html.includes('#js_content');
      
      if (isDeleted) {
        console.log(`⚠️ 文章 "${title}" 已被删除`);
        return false;
      }
      
      if (!hasContent) {
        console.log(`⚠️ 文章 "${title}" HTML不完整`);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('验证HTML时出错:', error);
      return false;
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 批量下载文章
   */
  public async downloadArticles(
    articles: Array<{ id: string; url: string; title: string }>,
    userId: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<DownloadResult[]> {
    const results: DownloadResult[] = [];
    
    for (let i = 0; i < articles.length; i++) {
      const article = articles[i];
      
      try {
        // 创建下载记录
        const downloadRecord = await prisma.articleDownload.create({
          data: {
            articleId: article.id,
            userId: userId,
            status: 'DOWNLOADING'
          }
        });
        
        // 下载文章
        const { html, size } = await this.downloadArticleHTML(article.url, article.title);
        
        // 这里可以保存HTML到文件系统或对象存储
        const filePath = await this.saveArticleHTML(html, article.id, article.title);
        
        // 更新下载记录
        await prisma.articleDownload.update({
          where: { id: downloadRecord.id },
          data: {
            status: 'COMPLETED',
            filePath: filePath,
            fileSize: size,
            downloadTime: new Date()
          }
        });
        
        results.push({
          success: true,
          filePath,
          fileSize: size
        });
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        
        // 更新下载记录为失败状态
        await prisma.articleDownload.updateMany({
          where: {
            articleId: article.id,
            userId: userId,
            status: 'DOWNLOADING'
          },
          data: {
            status: 'FAILED',
            errorMessage: errorMessage
          }
        });
        
        results.push({
          success: false,
          errorMessage
        });
      }
      
      // 通知进度
      onProgress?.(i + 1, articles.length);
      
      // 批次间延迟，避免请求过于频繁
      if (i < articles.length - 1) {
        await this.delay(2000);
      }
    }
    
    return results;
  }

  /**
   * 保存文章HTML到文件系统
   */
  private async saveArticleHTML(html: string, articleId: string, title: string): Promise<string> {
    // 这里应该实现文件保存逻辑
    // 可以保存到本地文件系统、云存储等
    // 暂时返回一个模拟的文件路径
    const fileName = `${articleId}_${Date.now()}.html`;
    const filePath = `downloads/${fileName}`;
    
    // TODO: 实际的文件保存逻辑
    console.log(`💾 保存文章HTML: ${title} -> ${filePath}`);
    
    return filePath;
  }
}
