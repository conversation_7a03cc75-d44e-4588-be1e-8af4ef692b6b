'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/app/components/AuthProvider';

interface DownloadButtonProps {
  articleId: string;
  articleTitle: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export function DownloadButton({
  articleId,
  articleTitle,
  className,
  variant = 'outline',
  size = 'sm'
}: DownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const { token } = useAuth();

  const handleDownload = async () => {
    if (isDownloading) return;

    if (!token) {
      toast.error('请先登录');
      return;
    }

    setIsDownloading(true);

    try {
      const response = await fetch('/api/articles/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          articleId,
          options: {
            withCredentials: true,
            timeout: 30
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '下载失败');
      }

      if (data.download && data.download.status === 'COMPLETED') {
        toast.success('文章已下载完成');
      } else {
        toast.success('下载任务已创建，请稍后查看下载状态');
      }
    } catch (error) {
      console.error('下载失败:', error);
      toast.error(error instanceof Error ? error.message : '下载失败');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleDownload}
      disabled={isDownloading}
      className={className}
      title={`下载文章: ${articleTitle}`}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      {size !== 'sm' && (
        <span className="ml-2">
          {isDownloading ? '下载中...' : '下载'}
        </span>
      )}
    </Button>
  );
}
