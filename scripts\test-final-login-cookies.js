#!/usr/bin/env node

/**
 * 测试最终登录请求中的Cookie获取
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testFinalLoginCookies() {
  console.log('🧪 测试最终登录请求中的Cookie获取...');
  
  let cookieAnalysis = {
    beforeLogin: 0,
    afterQRCode: 0,
    afterConfirm: 0,
    finalLogin: 0,
    criticalFields: {}
  };

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        const session = wechatLogin.getSession();
        cookieAnalysis.afterQRCode = session?.cookies?.length || 0;
        console.log(`🍪 二维码生成后Cookie数量: ${cookieAnalysis.afterQRCode}`);
      },
      
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        const session = wechatLogin.getSession();
        const currentCookieCount = session?.cookies?.length || 0;
        
        if (status === 'waiting') {
          console.log('⏳ 等待扫码...');
        } else if (status === 'scanned') {
          console.log('📱 已扫码，等待确认...');
        } else if (status === 'confirmed') {
          cookieAnalysis.afterConfirm = currentCookieCount;
          console.log(`🍪 确认后Cookie数量: ${cookieAnalysis.afterConfirm}`);
          
          // 分析Cookie内容
          if (session && session.cookies) {
            const cookieString = session.cookies.join('; ');
            cookieAnalysis.criticalFields = {
              data_ticket: cookieString.includes('data_ticket'),
              bizuin: cookieString.includes('bizuin'),
              rand_info: cookieString.includes('rand_info'),
              slave_sid: cookieString.includes('slave_sid'),
              slave_user: cookieString.includes('slave_user')
            };
            
            console.log('🔍 关键Cookie字段分析:');
            Object.entries(cookieAnalysis.criticalFields).forEach(([field, exists]) => {
              console.log(`  ${field}: ${exists ? '✅' : '❌'}`);
            });
          }
        }
      },
      
      onSuccess: async (userInfo, token) => {
        console.log('🎉 微信登录成功!');
        
        const session = wechatLogin.getSession();
        cookieAnalysis.finalLogin = session?.cookies?.length || 0;
        
        console.log('\n📊 Cookie获取分析报告:');
        console.log('='.repeat(50));
        console.log(`初始状态: ${cookieAnalysis.beforeLogin} cookies`);
        console.log(`二维码生成后: ${cookieAnalysis.afterQRCode} cookies`);
        console.log(`确认后: ${cookieAnalysis.afterConfirm} cookies`);
        console.log(`最终登录后: ${cookieAnalysis.finalLogin} cookies`);
        
        console.log('\n🔑 关键字段完整性:');
        const criticalCount = Object.values(cookieAnalysis.criticalFields).filter(Boolean).length;
        console.log(`完整度: ${criticalCount}/5`);
        
        if (criticalCount === 5) {
          console.log('✅ 所有关键Cookie字段都已获取，登录成功！');
        } else {
          console.log('⚠️ 部分关键Cookie字段缺失，可能影响后续功能');
        }
        
        // 显示实际的Cookie内容（脱敏）
        if (session && session.cookies) {
          console.log('\n🍪 Cookie内容预览:');
          session.cookies.forEach((cookie, index) => {
            const [name, value] = cookie.split('=');
            const maskedValue = value && value.length > 10 
              ? value.substring(0, 5) + '***' + value.substring(value.length - 3)
              : value;
            console.log(`  ${index + 1}. ${name}=${maskedValue}`);
          });
        }
        
        // 验证Cookie是否保存到数据库
        console.log('\n💾 验证数据库保存...');
        await verifyCookieInDatabase();
        
        process.exit(0);
      },
      
      onError: async (error) => {
        console.error('❌ 微信登录失败:', error);
        process.exit(1);
      }
    });

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    cookieAnalysis.beforeLogin = 0; // 初始状态没有Cookie
    
    const qrcode = await wechatLogin.startLogin();
    console.log('📱 请使用微信扫描二维码完成登录');
    
    // 设置超时
    setTimeout(() => {
      console.log('⏰ 测试超时，退出');
      wechatLogin.cancelLogin();
      
      console.log('\n📊 超时时的Cookie分析:');
      console.log(`当前Cookie数量: ${wechatLogin.getSession()?.cookies?.length || 0}`);
      
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyCookieInDatabase() {
  try {
    const { prisma } = await import('../lib/prisma');
    
    const criticalConfigs = [
      'wechat_article_data_ticket',
      'wechat_article_token',
      'wechat_article_cookie_string'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: { in: criticalConfigs }
      }
    });

    console.log('🔍 数据库验证结果:');
    criticalConfigs.forEach(configName => {
      const config = configs.find(c => c.name === configName);
      const hasValue = config && config.value && config.value.length > 0;
      console.log(`  ${configName}: ${hasValue ? '✅' : '❌'}`);
    });

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 数据库验证失败:', error);
  }
}

// 运行测试
testFinalLoginCookies();
