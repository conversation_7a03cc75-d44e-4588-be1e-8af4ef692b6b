#!/usr/bin/env node

/**
 * 测试登录速度优化效果
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testLoginSpeed() {
  console.log('🚀 测试登录速度优化效果...');
  
  let timestamps = {
    start: 0,
    qrGenerated: 0,
    scanned: 0,
    confirmed: 0,
    cookieChecked: 0,
    loginComplete: 0
  };

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        timestamps.qrGenerated = Date.now();
        const elapsed = timestamps.qrGenerated - timestamps.start;
        console.log(`📱 二维码生成 (+${elapsed}ms)`);
      },
      
      onStatusChange: async (status, data) => {
        const now = Date.now();
        
        if (status === 'scanned') {
          timestamps.scanned = now;
          const elapsed = now - timestamps.start;
          const fromQR = now - timestamps.qrGenerated;
          console.log(`👀 已扫码 (+${elapsed}ms, 从二维码生成: ${fromQR}ms)`);
          console.log('⚡ 轮询频率已调整为0.5秒，等待确认...');
          
        } else if (status === 'confirmed') {
          timestamps.confirmed = now;
          const elapsed = now - timestamps.start;
          const fromScanned = now - timestamps.scanned;
          console.log(`✅ 已确认 (+${elapsed}ms, 从扫码: ${fromScanned}ms)`);
          console.log('⚡ 轮询频率已调整为0.2秒，快速处理中...');
        }
      },
      
      onSuccess: async (userInfo, token) => {
        timestamps.loginComplete = Date.now();
        
        console.log('\n🎉 登录完成！时间分析:');
        console.log('='.repeat(50));
        
        const totalTime = timestamps.loginComplete - timestamps.start;
        const qrTime = timestamps.qrGenerated - timestamps.start;
        const scanTime = timestamps.scanned - timestamps.qrGenerated;
        const confirmTime = timestamps.confirmed - timestamps.scanned;
        const processTime = timestamps.loginComplete - timestamps.confirmed;
        
        console.log(`📊 总耗时: ${totalTime}ms`);
        console.log(`  1. 二维码生成: ${qrTime}ms`);
        console.log(`  2. 等待扫码: ${scanTime}ms`);
        console.log(`  3. 扫码到确认: ${confirmTime}ms`);
        console.log(`  4. 确认到完成: ${processTime}ms`);
        
        console.log('\n🔍 性能分析:');
        if (confirmTime < 2000) {
          console.log('✅ 扫码到确认时间优秀 (<2秒)');
        } else if (confirmTime < 5000) {
          console.log('⚠️ 扫码到确认时间一般 (2-5秒)');
        } else {
          console.log('❌ 扫码到确认时间较慢 (>5秒)');
        }
        
        if (processTime < 3000) {
          console.log('✅ 确认到完成时间优秀 (<3秒)');
        } else if (processTime < 8000) {
          console.log('⚠️ 确认到完成时间一般 (3-8秒)');
        } else {
          console.log('❌ 确认到完成时间较慢 (>8秒)');
        }
        
        console.log('\n👤 用户信息:', userInfo);
        console.log('🔑 Token:', token ? `${token.substring(0, 10)}...` : 'none');
        
        // 验证Cookie保存
        await verifyCookieSave();
        
        process.exit(0);
      },
      
      onError: async (error) => {
        console.error('❌ 登录失败:', error);
        process.exit(1);
      },
      
      onCookieSave: async (cookies, token) => {
        timestamps.cookieChecked = Date.now();
        const elapsed = timestamps.cookieChecked - timestamps.start;
        const fromConfirmed = timestamps.cookieChecked - timestamps.confirmed;
        
        console.log(`🍪 Cookie检查完成 (+${elapsed}ms, 从确认: ${fromConfirmed}ms)`);
        
        // 分析Cookie质量
        const criticalFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
        let validCount = 0;
        
        criticalFields.forEach(field => {
          if (cookies[field] && cookies[field].length > 0) {
            validCount++;
          }
        });
        
        console.log(`🎯 Cookie完整度: ${validCount}/${criticalFields.length}`);
        
        if (validCount >= 3) {
          console.log('✅ Cookie足够完整，将使用快速登录流程');
        } else {
          console.log('⚠️ Cookie不够完整，将使用完整登录流程');
        }
        
        // 模拟保存到数据库
        console.log('💾 保存Cookie到数据库...');
      }
    });

    // 开始计时
    timestamps.start = Date.now();
    console.log('🚀 开始登录流程...');
    
    const qrcode = await wechatLogin.startLogin();
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('⏰ 初始轮询频率: 1秒');
    
    // 设置超时
    setTimeout(() => {
      const elapsed = Date.now() - timestamps.start;
      console.log(`⏰ 测试超时 (${elapsed}ms)，退出`);
      
      if (timestamps.scanned > 0) {
        const scanToTimeout = Date.now() - timestamps.scanned;
        console.log(`📊 从扫码到超时: ${scanToTimeout}ms`);
      }
      
      wechatLogin.cancelLogin();
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyCookieSave() {
  try {
    console.log('\n🔍 验证Cookie保存状态...');
    
    const { prisma } = await import('../lib/prisma');
    
    const criticalConfigs = [
      'wechat_article_data_ticket',
      'wechat_article_token',
      'wechat_article_cookie_string'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: { in: criticalConfigs }
      }
    });

    console.log('📊 数据库验证结果:');
    let savedCount = 0;
    criticalConfigs.forEach(configName => {
      const config = configs.find(c => c.name === configName);
      const hasValue = config && config.value && config.value.length > 0;
      if (hasValue) savedCount++;
      console.log(`  ${configName}: ${hasValue ? '✅' : '❌'}`);
    });

    if (savedCount === criticalConfigs.length) {
      console.log('✅ Cookie已成功保存到数据库');
    } else {
      console.log('⚠️ Cookie保存可能不完整');
    }

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ Cookie验证失败:', error);
  }
}

// 运行测试
testLoginSpeed();
