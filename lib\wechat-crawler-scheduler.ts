import { prisma } from './prisma';
import { WebhookNotificationService, ArticleInfo } from './webhook-notification';

/**
 * 微信公众号文章定时爬取调度器
 */
export class WechatCrawlerScheduler {
  private static instance: WechatCrawlerScheduler;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 5;

  private constructor() {}

  public static getInstance(): WechatCrawlerScheduler {
    if (!WechatCrawlerScheduler.instance) {
      WechatCrawlerScheduler.instance = new WechatCrawlerScheduler();
    }
    return WechatCrawlerScheduler.instance;
  }



  /**
   * 检查当前是否在允许执行的时间范围内（北京时间6:00-23:00）
   */
  private isInAllowedTimeRange(): boolean {
    const now = new Date();
    // 转换为北京时间（UTC+8）
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
    const hour = beijingTime.getUTCHours();

    // 允许执行时间：6:00-23:00（北京时间）
    const isAllowed = hour >= 6 && hour < 23;

    if (!isAllowed) {
      console.log(`⏰ 当前北京时间 ${hour}:${beijingTime.getUTCMinutes().toString().padStart(2, '0')}，不在允许执行时间范围内（6:00-23:00），跳过执行`);
    }

    return isAllowed;
  }

  /**
   * 启动定时任务
   */
  public start(): void {
    if (this.intervalId) {
      console.log('⚠️ 定时任务已经在运行中');
      return;
    }

    console.log('🚀 启动微信公众号文章定时爬取任务');
    
    // 立即执行一次
    this.executeTask();
    
    // 每2分钟执行一次
    this.intervalId = setInterval(() => {
      this.executeTask();
    }, 2 * 60 * 1000); // 2分钟 = 120秒 = 120000毫秒
  }

  /**
   * 停止定时任务
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('⏹️ 微信公众号文章定时爬取任务已停止');
    }
  }

  /**
   * 重置失败计数器
   */
  public resetFailureCount(): void {
    this.consecutiveFailures = 0;
    console.log('🔄 失败计数器已重置');
  }

  /**
   * 获取任务状态
   */
  public getStatus(): {
    isRunning: boolean;
    nextExecution?: Date;
    consecutiveFailures: number;
    maxConsecutiveFailures: number;
    isInAllowedTime: boolean;
  } {
    return {
      isRunning: !!this.intervalId,
      nextExecution: this.intervalId ? new Date(Date.now() + 2 * 60 * 1000) : undefined,
      consecutiveFailures: this.consecutiveFailures,
      maxConsecutiveFailures: this.maxConsecutiveFailures,
      isInAllowedTime: this.isInAllowedTimeRange()
    };
  }

  /**
   * 执行爬取任务
   */
  private async executeTask(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 上一个任务还在执行中，跳过本次执行');
      return;
    }

    // 检查是否在允许执行的时间范围内
    if (!this.isInAllowedTimeRange()) {
      return;
    }

    // 检查连续失败次数
    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
      console.log(`❌ 连续失败 ${this.consecutiveFailures} 次，已达到最大限制 ${this.maxConsecutiveFailures} 次，停止定时任务`);
      this.stop();
      return;
    }

    this.isRunning = true;
    console.log('🔄 开始执行定时爬取任务:', new Date().toISOString());

    try {
      // 获取启用了定时爬取的公众号
      // 排除30分钟内发布过新文章的公众号（避免频繁爬取）
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      // 先获取所有启用爬取的公众号
      const allEnabledAccounts = await prisma.wechatAccount.findMany({
        where: {
          enableCrawling: true,
          fakeid: { not: null }
        },
        include: {
          articles: {
            orderBy: {
              publishDate: 'desc'
            },
            take: 1 // 只取最新的一篇文章
          }
        }
      });

      // 过滤出符合30分钟冷却期条件的公众号
      const enabledAccounts = allEnabledAccounts.filter(account => {
        // 如果没有文章，可以执行
        if (account.articles.length === 0) {
          return true;
        }

        // 检查最新文章的发布时间
        const latestArticle = account.articles[0];
        const timeDiff = Date.now() - latestArticle.publishDate.getTime();
        const minutesAgo = Math.floor(timeDiff / (1000 * 60));

        // 如果最新文章发布时间超过30分钟，可以执行
        return minutesAgo >= 30;
      });

      console.log(`📊 找到 ${enabledAccounts.length} 个可执行定时爬取的公众号`);

      const skippedCount = allEnabledAccounts.length - enabledAccounts.length;
      if (skippedCount > 0) {
        console.log(`⏸️ ${skippedCount} 个公众号因30分钟冷却期被跳过`);

        // 显示被跳过的公众号详情
        const skippedAccounts = allEnabledAccounts.filter(account =>
          !enabledAccounts.some(enabled => enabled.id === account.id)
        );

        skippedAccounts.forEach(account => {
          const lastArticleTime = account.articles[0]?.publishDate;
          if (lastArticleTime) {
            const minutesAgo = Math.floor((Date.now() - lastArticleTime.getTime()) / (1000 * 60));
            console.log(`   - ${account.name}: 最新文章发布于 ${minutesAgo} 分钟前`);
          }
        });
      }

      if (enabledAccounts.length === 0) {
        console.log('📝 没有启用定时爬取的公众号，跳过执行');
        return;
      }

      // 获取微信登录凭证
      const credentials = await this.getWechatCredentials();
      console.log('🔑 微信凭证获取结果:', {
        hasCredentials: !!credentials,
        hasToken: credentials ? !!credentials.token : false,
        hasCookie: credentials ? !!credentials.cookie : false,
        hasDataTicket: credentials ? !!credentials.dataTicket : false,
        tokenLength: credentials?.token ? credentials.token.length : 0,
        cookieLength: credentials?.cookie ? credentials.cookie.length : 0
      });

      if (!credentials) {
        console.log('❌ 未找到有效的微信登录凭证，跳过执行');
        return;
      }

      // 并发处理多个公众号（限制并发数）
      const concurrency = 3; // 最多同时处理3个公众号
      for (let i = 0; i < enabledAccounts.length; i += concurrency) {
        const batch = enabledAccounts.slice(i, i + concurrency);
        await Promise.allSettled(
          batch.map(account => this.crawlAccountArticles(account, credentials))
        );
        
        // 批次间稍作延迟，避免请求过于频繁
        if (i + concurrency < enabledAccounts.length) {
          await this.delay(1000); // 延迟1秒
        }
      }

    } catch (error) {
      console.error('❌ 定时爬取任务执行失败:', error);
      this.consecutiveFailures++;
      console.log(`⚠️ 连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
    } finally {
      this.isRunning = false;
      console.log('✅ 定时爬取任务执行完成:', new Date().toISOString());
    }
  }

  /**
   * 获取微信登录凭证
   */
  private async getWechatCredentials(): Promise<{
    token: string;
    cookie: string;
    dataTicket: string;
  } | null> {
    try {
      const configNames = [
        'wechat_article_token',
        'wechat_article_data_ticket',
        'wechat_article_rand_info',
        'wechat_article_bizuin',
        'wechat_article_slave_sid',
        'wechat_article_slave_user'
      ];

      const configs = await prisma.systemConfig.findMany({
        where: {
          name: { in: configNames }
        }
      });

      const configMap: Record<string, string> = {};
      configs.forEach(config => {
        configMap[config.name] = config.value;
      });

      const token = configMap.wechat_article_token;
      const dataTicket = configMap.wechat_article_data_ticket;

      // 动态构建Cookie字符串
      const cookieFields = [
        { key: 'data_ticket', name: 'wechat_article_data_ticket' },
        { key: 'rand_info', name: 'wechat_article_rand_info' },
        { key: 'bizuin', name: 'wechat_article_bizuin' },
        { key: 'slave_sid', name: 'wechat_article_slave_sid' },
        { key: 'slave_user', name: 'wechat_article_slave_user' }
      ];

      const cookiePairs = cookieFields
        .filter(field => configMap[field.name])
        .map(field => `${field.key}=${configMap[field.name]}`);

      const cookie = cookiePairs.join(';');

      if (!token || !cookie || !dataTicket) {
        console.log('⚠️ 微信登录凭证不完整:', {
          hasToken: !!token,
          hasCookie: !!cookie,
          hasDataTicket: !!dataTicket,
          cookieFieldsCount: cookiePairs.length
        });
        return null;
      }

      return { token, cookie, dataTicket };
    } catch (error) {
      console.error('❌ 获取微信登录凭证失败:', error);
      return null;
    }
  }

  /**
   * 爬取单个公众号的文章
   */
  private async crawlAccountArticles(
    account: any,
    credentials: { token: string; cookie: string; dataTicket: string }
  ): Promise<void> {
    console.log(`🔍 开始爬取公众号: ${account.name} (${account.fakeid})`);

    try {
      // 更新爬取状态
      await prisma.wechatAccount.update({
        where: { id: account.id },
        data: {
          crawlStatus: 'running',
          crawlError: null
        }
      });

      // 调用微信文章发布接口
      const articles = await this.fetchWechatArticles(account.fakeid, credentials);

      console.log(`🔍 公众号 ${account.name} API返回结果:`, {
        articlesType: typeof articles,
        articlesIsArray: Array.isArray(articles),
        articlesLength: articles ? articles.length : 'null',
        articlesContent: articles ? JSON.stringify(articles, null, 2) : 'null'
      });

      if (articles && articles.length > 0) {
        // 保存新文章到数据库
        const savedCount = await this.saveNewArticles(account.id, articles);
        console.log(`✅ 公众号 ${account.name} 爬取完成，新增 ${savedCount} 篇文章`);
        // 成功执行，重置失败计数器
        this.consecutiveFailures = 0;
      } else {
        console.log(`📝 公众号 ${account.name} 没有新文章 (articles: ${articles ? 'empty array' : 'null'})`);
        // 即使没有新文章，API调用成功也重置失败计数器
        this.consecutiveFailures = 0;
      }

      // 更新爬取状态
      await prisma.wechatAccount.update({
        where: { id: account.id },
        data: {
          crawlStatus: 'idle',
          lastCrawlTime: new Date(),
          crawlError: null
        }
      });

    } catch (error) {
      console.error(`❌ 爬取公众号 ${account.name} 失败:`, error);
      console.error(`❌ 错误详情:`, {
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : 'No stack trace',
        accountInfo: {
          id: account.id,
          name: account.name,
          fakeid: account.fakeid
        }
      });

      // 更新错误状态
      await prisma.wechatAccount.update({
        where: { id: account.id },
        data: {
          crawlStatus: 'error',
          lastCrawlTime: new Date(),
          crawlError: error instanceof Error ? error.message : '未知错误'
        }
      });
    }
  }

  /**
   * 调用微信文章发布接口
   */
  private async fetchWechatArticles(
    fakeid: string,
    credentials: { token: string; cookie: string; dataTicket: string }
  ): Promise<any[] | null> {
    const apiUrl = 'https://mp.weixin.qq.com/cgi-bin/appmsgpublish';
    const params = new URLSearchParams({
      sub: 'list',
      search_field: '1',
      begin: '0',
      count: '1', // 获取最新1篇文章
      fakeid: fakeid,
      type: '101_1',
      query: '',
      token: credentials.token,
      lang: 'zh_CN',
      f: 'json',
      ajax: '1'
    });

    const fullUrl = `${apiUrl}?${params}`;
    console.log(`📡 调用微信文章接口 (fakeid: ${fakeid})`);
    console.log(`🔗 请求URL: ${fullUrl}`);
    console.log(`🍪 Cookie长度: ${credentials.cookie.length} 字符`);
    console.log(`🎫 Token: ${credentials.token.substring(0, 20)}...`);

    try {
      const startTime = Date.now();

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Cookie': credentials.cookie,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': 'https://mp.weixin.qq.com/',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      const responseTime = Date.now() - startTime;
      console.log(`⏱️ 请求耗时: ${responseTime}ms`);
      console.log(`📊 响应状态: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.error(`❌ HTTP错误: ${response.status} ${response.statusText}`);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log(`📄 响应内容长度: ${responseText.length} 字符`);
      console.log(`📝 响应内容预览: ${responseText.substring(0, 200)}...`);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ JSON解析失败:', parseError);
        console.error('📄 原始响应:', responseText);
        throw new Error('响应不是有效的JSON格式');
      }

      console.log(`🔍 解析后的数据结构:`, {
        hasBaseResp: !!data.base_resp,
        baseRespRet: data.base_resp?.ret,
        baseRespErrMsg: data.base_resp?.err_msg,
        hasPublishPage: !!data.publish_page,
        publishPageType: typeof data.publish_page,
        publishPagePreview: typeof data.publish_page === 'string' ? data.publish_page.substring(0, 100) + '...' : data.publish_page
      });

      if (data.base_resp?.ret !== 0) {
        const errorMsg = `微信API错误: ${data.base_resp?.err_msg || '未知错误'} (ret: ${data.base_resp?.ret})`;
        console.error(`❌ ${errorMsg}`);
        this.consecutiveFailures++;
        console.log(`⚠️ API调用失败，连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
        throw new Error(errorMsg);
      }

      // 解析 publish_page 字段（它是一个JSON字符串）
      let publishPage;
      try {
        if (typeof data.publish_page === 'string') {
          publishPage = JSON.parse(data.publish_page);
          console.log(`📋 publish_page解析成功:`, {
            totalCount: publishPage.total_count,
            publishCount: publishPage.publish_count,
            publishListLength: publishPage.publish_list?.length || 0
          });
        } else {
          publishPage = data.publish_page;
          console.log(`📋 publish_page已是对象:`, publishPage);
        }
      } catch (parseError) {
        console.error('❌ publish_page JSON解析失败:', parseError);
        console.error('📄 原始publish_page:', data.publish_page);
        throw new Error('publish_page解析失败');
      }

      const publishList = publishPage?.publish_list || [];
      console.log(`✅ 成功获取 ${publishList.length} 个发布记录`);

      // 解析文章列表
      const allArticles: any[] = [];

      if (publishList.length > 0) {
        // 获取第一个发布记录
        const firstPublish = publishList[0];
        console.log(`📋 处理发布记录:`, {
          publishType: firstPublish.publish_type,
          hasPublishInfo: !!firstPublish.publish_info
        });

        // 解析 publish_info 字段（JSON字符串）
        let publishInfo;
        try {
          if (typeof firstPublish.publish_info === 'string') {
            publishInfo = JSON.parse(firstPublish.publish_info);
          } else {
            publishInfo = firstPublish.publish_info;
          }
        } catch (error) {
          console.error(`❌ publish_info解析失败:`, error);
          publishInfo = {};
        }

        console.log(`� publish_info解析结果:`, {
          hasAppmsgInfo: !!publishInfo.appmsg_info,
          appmsgInfoLength: publishInfo.appmsg_info?.length || 0,
          hasAppmsgex: !!publishInfo.appmsgex,
          appmsgexLength: publishInfo.appmsgex?.length || 0,
          publishTime: publishInfo.publish_time,
          sentTime: publishInfo.sent_info?.time
        });

        // 从 appmsg_info 列表中提取文章（主要文章）
        const appmsgInfoList = publishInfo.appmsg_info || [];

        appmsgInfoList.forEach((article: any, index: number) => {
          const isMainArticle = index === 0;
          console.log(`📰 ${isMainArticle ? '主' : '副'}文章 ${index + 1} (来自appmsg_info):`, {
            title: article.title || '无标题',
            appmsgid: article.appmsgid,
            contentUrl: article.content_url?.substring(0, 50) + '...' || '无链接',
            hasCover: !!article.cover,
            digest: article.digest?.substring(0, 30) + '...' || '无摘要'
          });

          allArticles.push({
            title: article.title,
            link: article.content_url,
            cover: article.cover,
            digest: article.digest,
            create_time: publishInfo.sent_info?.time || publishInfo.publish_time,
            appmsgid: article.appmsgid,
            isMainArticle,
            publishTime: publishInfo.sent_info?.time || publishInfo.publish_time
          });
        });

        // 从 appmsgex 列表中提取文章（副文章）
        const appmsgexList = publishInfo.appmsgex || [];

        appmsgexList.forEach((article: any, index: number) => {
          console.log(`📰 副文章 ${index + 1} (来自appmsgex):`, {
            title: article.title || '无标题',
            createTime: article.create_time,
            link: article.link?.substring(0, 50) + '...' || '无链接',
            hasCover: !!article.cover,
            digest: article.digest?.substring(0, 30) + '...' || '无摘要'
          });

          allArticles.push({
            ...article,
            isMainArticle: false,
            publishTime: publishInfo.sent_info?.time || publishInfo.publish_time || article.create_time
          });
        });
      }

      console.log(`📊 总共解析出 ${allArticles.length} 篇文章`);
      return allArticles;
    } catch (error) {
      console.error(`❌ 调用微信文章接口失败 (fakeid: ${fakeid}):`, error);
      console.error(`❌ 错误详情:`, {
        errorType: typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : 'No stack trace'
      });
      this.consecutiveFailures++;
      console.log(`⚠️ 网络请求失败，连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
      throw error;
    }
  }

  /**
   * 保存新文章到数据库
   */
  private async saveNewArticles(accountId: string, articles: any[]): Promise<number> {
    let savedCount = 0;
    const savedMainArticles: any[] = [];
    const savedSubArticles: any[] = [];

    // 先保存所有文章到数据库
    for (const article of articles) {
      try {
        const title = article.title || '无标题';
        const url = article.link || '';
        const summary = article.digest || '';
        const cover = article.cover || '';
        const isMainArticle = article.isMainArticle || false;

        // 处理发布时间（create_time 是秒级时间戳）
        const publishTime = article.create_time
          ? new Date(article.create_time * 1000)
          : (article.publishTime ? new Date(article.publishTime * 1000) : new Date());

        console.log(`💾 准备保存${isMainArticle ? '主' : '副'}文章: ${title}`);
        console.log(`🔗 文章URL: ${url}`);
        console.log(`📅 发布时间: ${publishTime.toISOString()}`);
        console.log(`🖼️ 封面: ${cover ? '有' : '无'}`);

        // 检查文章是否已存在（根据URL）
        const existingArticle = await prisma.article.findFirst({
          where: {
            wechatAccountId: accountId,
            url: url
          }
        });

        if (existingArticle) {
          // 检查URL是否一致，如果不一致则更新为新URL
          if (existingArticle.url !== url) {
            console.log(`🔄 文章URL不一致，更新URL: ${title}`);
            console.log(`  旧URL: ${existingArticle.url}`);
            console.log(`  新URL: ${url}`);

            try {
              await prisma.article.update({
                where: { id: existingArticle.id },
                data: {
                  url: url,
                  updatedAt: new Date()
                }
              });
              console.log(`✅ URL更新成功: ${title}`);
            } catch (updateError) {
              console.error(`❌ URL更新失败: ${title}`, updateError);
            }
          } else {
            console.log(`⏭️ 文章已存在，跳过: ${title}`);
          }
          continue;
        }

        // 保存新文章
        const newArticle = await prisma.article.create({
          data: {
            title: title,
            url: url,
            summary: summary,
            coverImage: cover || null, // 保存封面图片
            publishDate: publishTime,
            wechatAccountId: accountId,
            author: article.author || '', // 从文章数据中获取作者
            tags: '' // 可以后续添加标签功能
          }
        });

        console.log(`✅ ${isMainArticle ? '主' : '副'}文章保存成功: ${title} (ID: ${newArticle.id})`);
        savedCount++;

        // 分类收集保存的文章
        if (isMainArticle) {
          savedMainArticles.push(newArticle);
        } else {
          savedSubArticles.push(newArticle);
        }
      } catch (error) {
        console.error('❌ 保存文章失败:', error);
        console.error('📄 文章数据:', {
          title: article.title,
          link: article.link,
          createTime: article.create_time,
          isMainArticle: article.isMainArticle
        });
      }
    }

    // 发送webhook通知
    // 1. 主文章逐个发送通知
    for (const mainArticle of savedMainArticles) {
      await this.sendWebhookNotifications(mainArticle, accountId, true);
    }

    // 2. 副文章合并发送通知
    if (savedSubArticles.length > 0) {
      await this.sendSubArticlesNotification(savedSubArticles, accountId);
    }

    console.log(`📊 本次保存统计: 新增 ${savedCount} 篇文章 (主文章: ${savedMainArticles.length}, 副文章: ${savedSubArticles.length})`);
    return savedCount;
  }

  /**
   * 发送副文章合并通知
   */
  private async sendSubArticlesNotification(subArticles: any[], accountId: string): Promise<void> {
    try {
      // 获取订阅该公众号且启用了webhook的用户
      const subscriptions = await prisma.subscription.findMany({
        where: {
          wechatAccountId: accountId,
          isActive: true,
          user: {
            webhookEnabled: true,
            webhookUrl: {
              not: null
            },
            webhookType: {
              not: null
            }
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              webhookUrl: true,
              webhookType: true
            }
          },
          wechatAccount: {
            select: {
              name: true
            }
          }
        }
      });

      if (subscriptions.length === 0) {
        console.log('📭 没有用户启用webhook通知');
        return;
      }

      console.log(`📬 准备向 ${subscriptions.length} 个用户发送副文章合并通知 (${subArticles.length}篇)`);

      // 构造副文章信息
      const subArticlesInfo = {
        articles: subArticles,
        accountName: subscriptions[0].wechatAccount.name,
        publishDate: subArticles[0].publishDate.toISOString()
      };

      // 并发发送通知（限制并发数避免过载）
      const concurrency = 5;
      for (let i = 0; i < subscriptions.length; i += concurrency) {
        const batch = subscriptions.slice(i, i + concurrency);
        await Promise.allSettled(
          batch.map(async (subscription) => {
            try {
              const success = await WebhookNotificationService.sendSubArticlesNotification(
                subscription.user.webhookUrl!,
                subscription.user.webhookType!,
                subArticlesInfo
              );

              if (success) {
                console.log(`✅ 成功向用户 ${subscription.user.email} 发送副文章合并通知`);
              } else {
                console.log(`❌ 向用户 ${subscription.user.email} 发送副文章合并通知失败`);
              }
            } catch (error) {
              console.error(`❌ 向用户 ${subscription.user.email} 发送副文章合并通知异常:`, error);
            }
          })
        );

        // 批次间稍作延迟
        if (i + concurrency < subscriptions.length) {
          await this.delay(100);
        }
      }
    } catch (error) {
      console.error('❌ 发送副文章合并通知失败:', error);
    }
  }

  /**
   * 发送webhook通知给订阅该公众号的用户
   */
  private async sendWebhookNotifications(article: any, accountId: string, isMainArticle: boolean = false): Promise<void> {
    try {
      // 获取订阅该公众号且启用了webhook的用户
      const subscriptions = await prisma.subscription.findMany({
        where: {
          wechatAccountId: accountId,
          isActive: true,
          user: {
            webhookEnabled: true,
            webhookUrl: {
              not: null
            },
            webhookType: {
              not: null
            }
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              webhookUrl: true,
              webhookType: true
            }
          },
          wechatAccount: {
            select: {
              name: true
            }
          }
        }
      });

      if (subscriptions.length === 0) {
        console.log('📭 没有用户启用webhook通知');
        return;
      }

      console.log(`📬 准备向 ${subscriptions.length} 个用户发送${isMainArticle ? '主文章' : '副文章'}webhook通知`);

      // 构造文章信息
      const articleInfo: ArticleInfo = {
        title: article.title,
        url: article.url,
        summary: article.summary,
        publishDate: article.publishDate.toISOString(),
        accountName: subscriptions[0].wechatAccount.name,
        coverImage: article.coverImage,
        isMainArticle: isMainArticle
      };

      // 并发发送通知（限制并发数避免过载）
      const concurrency = 5;
      for (let i = 0; i < subscriptions.length; i += concurrency) {
        const batch = subscriptions.slice(i, i + concurrency);
        await Promise.allSettled(
          batch.map(async (subscription) => {
            try {
              const success = await WebhookNotificationService.sendNewArticleNotification(
                subscription.user.webhookUrl!,
                subscription.user.webhookType!,
                articleInfo
              );

              if (success) {
                console.log(`✅ 成功向用户 ${subscription.user.email} 发送webhook通知`);
              } else {
                console.log(`❌ 向用户 ${subscription.user.email} 发送webhook通知失败`);
              }
            } catch (error) {
              console.error(`❌ 向用户 ${subscription.user.email} 发送webhook通知异常:`, error);
            }
          })
        );

        // 批次间稍作延迟
        if (i + concurrency < subscriptions.length) {
          await this.delay(100);
        }
      }
    } catch (error) {
      console.error('❌ 发送webhook通知失败:', error);
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
