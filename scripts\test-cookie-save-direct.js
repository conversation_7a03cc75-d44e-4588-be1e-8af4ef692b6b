#!/usr/bin/env node

/**
 * 测试直接Cookie保存功能
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testDirectCookieSave() {
  console.log('🧪 测试直接Cookie保存功能...');
  
  // 模拟Cookie保存回调
  const mockCookieSave = async (cookies, token) => {
    console.log('📥 收到Cookie保存回调:');
    console.log('  Cookie数量:', Object.keys(cookies).length);
    console.log('  Token:', token ? `${token.substring(0, 8)}...` : 'none');
    
    // 检查关键字段
    const criticalFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
    let validCount = 0;
    
    console.log('🔍 Cookie字段检查:');
    criticalFields.forEach(field => {
      const hasValue = cookies[field] && cookies[field].length > 0;
      if (hasValue) validCount++;
      console.log(`  ${field}: ${hasValue ? '✅' : '❌'}`);
    });
    
    console.log(`🎯 关键字段完整度: ${validCount}/${criticalFields.length}`);
    
    // 模拟保存到数据库
    try {
      const { prisma } = await import('../lib/prisma');
      
      // 构建Cookie字符串
      const cookieString = Object.entries(cookies)
        .map(([key, value]) => `${key}=${value}`)
        .join(';');

      // 保存到数据库的配置项
      const configs = [
        { name: 'wechat_article_data_ticket', value: cookies.data_ticket || '' },
        { name: 'wechat_article_rand_info', value: cookies.rand_info || '' },
        { name: 'wechat_article_bizuin', value: cookies.bizuin || '' },
        { name: 'wechat_article_slave_sid', value: cookies.slave_sid || '' },
        { name: 'wechat_article_slave_user', value: cookies.slave_user || '' },
        { name: 'wechat_article_token', value: token || 'callback_save_token' },
        { name: 'wechat_article_cookie_string', value: cookieString },
        { name: 'wechat_article_session_id', value: 'test_session_' + Date.now() },
        { name: 'wechat_article_login_time', value: new Date().toISOString() }
      ];

      // 使用upsert更新或创建配置
      for (const config of configs) {
        await prisma.systemConfig.upsert({
          where: { name: config.name },
          update: {
            value: config.value,
            updatedAt: new Date()
          },
          create: {
            name: config.name,
            value: config.value
          }
        });
      }

      await prisma.$disconnect();
      console.log('✅ Cookie通过回调成功保存到数据库');
      
    } catch (error) {
      console.error('❌ Cookie保存失败:', error);
      throw error;
    }
  };

  try {
    // 创建微信登录实例，包含Cookie保存回调
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
      },
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        if (status === 'confirmed') {
          console.log('✅ 登录确认成功！');
        }
      },
      onSuccess: async (userInfo, token) => {
        console.log('🎉 微信登录成功!');
        console.log('👤 用户信息:', userInfo);
        console.log('🔑 Token:', token);
        
        // 验证Cookie是否已保存到数据库
        await verifyCookieInDatabase();
        
        process.exit(0);
      },
      onError: async (error) => {
        console.error('❌ 微信登录失败:', error);
        process.exit(1);
      },
      onCookieSave: mockCookieSave // 添加Cookie保存回调
    });

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('⏰ 等待扫码中...');
    
    // 设置超时
    setTimeout(() => {
      console.log('⏰ 测试超时，退出');
      wechatLogin.cancelLogin();
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyCookieInDatabase() {
  try {
    console.log('🔍 验证Cookie是否已保存到数据库...');
    
    const { prisma } = await import('../lib/prisma');
    
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    console.log('📊 数据库中的配置数量:', configs.length);
    
    const configMap = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 检查每个配置
    let validCount = 0;
    for (const name of configNames) {
      const value = configMap[name];
      const hasValue = value && value.length > 0;
      if (hasValue) validCount++;
      
      const status = hasValue ? '✅' : '❌';
      const preview = value ? (value.length > 20 ? value.substring(0, 20) + '...' : value) : '未设置';
      console.log(`  ${name}: ${status} ${preview}`);
    }

    console.log(`🎯 配置完整度: ${validCount}/${configNames.length}`);

    if (validCount >= 6) {
      console.log('✅ Cookie已成功保存到数据库');
    } else {
      console.log('❌ Cookie保存不完整');
    }

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 验证数据库Cookie失败:', error);
  }
}

// 运行测试
testDirectCookieSave();
