# 微信扫码登录Cookie保存问题修复

## 问题描述

用户反馈：微信扫码登录成功后，token被成功保存到数据库，但是相关的cookie没有保存。

## 问题根因分析

通过代码分析发现，问题主要出现在以下几个方面：

### 1. Cookie获取时机正确性

**正确的Cookie获取流程**:
- Cookie应该在最终登录请求（`/cgi-bin/scanloginqrcode?action=login`）的响应中直接获取
- 不需要访问其他页面来获取Cookie
- 最终登录请求的响应头中包含了所有必要的认证Cookie

**当前实现**:
- ✅ 已在最终登录请求中正确获取Cookie
- ✅ 添加了关键Cookie字段的检查
- ✅ 移除了不必要的额外页面访问

### 2. 硬编码域名问题

**问题代码**:
```typescript
const baseUrl = process.env.NODE_ENV === 'production'
  ? 'https://your-domain.com'  // ❌ 硬编码的无效域名
  : 'http://localhost:3000';
```

**问题影响**:
- 在生产环境中，API调用会发送到无效的域名 `https://your-domain.com`
- 导致网络请求失败，cookie保存API无法被正确调用
- 虽然token保存成功（通过其他途径），但cookie保存失败

### 2. 错误处理不完善

**原始问题**:
- 当API调用失败时，没有备用的保存机制
- 错误信息不够详细，难以调试
- 没有回退到直接数据库保存的机制

## 修复方案

### 1. 动态域名获取

**修复后的代码**:
```typescript
const baseUrl = typeof window !== 'undefined' 
  ? window.location.origin 
  : process.env.NEXTAUTH_URL || process.env.VERCEL_URL 
  ? `https://${process.env.VERCEL_URL}` 
  : 'http://localhost:3000';
```

**修复说明**:
- 在浏览器环境中使用 `window.location.origin`
- 在服务器环境中优先使用 `NEXTAUTH_URL` 环境变量
- 在Vercel部署中使用 `VERCEL_URL` 环境变量
- 开发环境回退到 `localhost:3000`

### 2. 增强错误处理和备用机制

**新增功能**:
- 详细的错误日志记录
- 网络失败时的直接数据库保存备用机制
- 更好的错误信息提示

**备用保存方法**:
```typescript
private async saveCookiesDirectly(cookieObject: Record<string, string>): Promise<void> {
  // 直接通过Prisma保存到数据库，绕过API调用
}
```

### 3. 改进的调试信息

**新增日志**:
- API调用的完整URL信息
- 详细的错误状态和响应内容
- Cookie保存过程的每个步骤

## 修复验证

### 1. 手动测试

在管理页面进行微信扫码登录：

1. 访问 `/admin/crawler` 页面
2. 点击"创建登录会话"
3. 扫描二维码完成登录
4. 检查页面上的"关键参数"部分是否显示了完整的cookie信息

### 2. 使用测试脚本

```bash
# 运行测试脚本
node scripts/test-cookie-save-fix.js
```

测试脚本会：
- 创建登录会话
- 等待扫码登录
- 验证cookie是否正确保存到数据库
- 显示详细的保存状态

### 3. 数据库验证

检查 `SystemConfig` 表中是否包含以下配置：

```sql
SELECT name, value FROM SystemConfig 
WHERE name IN (
  'wechat_article_data_ticket',
  'wechat_article_rand_info', 
  'wechat_article_bizuin',
  'wechat_article_slave_sid',
  'wechat_article_slave_user',
  'wechat_article_token',
  'wechat_article_cookie_string',
  'wechat_article_session_id',
  'wechat_article_login_time'
);
```

## 预期结果

修复后，微信扫码登录成功时应该看到：

### 1. 控制台日志
```
🌐 使用API基础URL: https://your-actual-domain.com
💾 保存Cookie到数据库...
🍪 准备保存的Cookie数量: 5
✅ Cookie保存成功: { success: true, message: 'Cookie保存成功' }
```

### 2. 管理页面显示
- "微信登录凭证" 状态显示为 "已登录"
- "关键参数" 部分显示完整的cookie值：
  - Token: 显示实际的token值
  - data_ticket: 显示实际值
  - rand_info: 显示实际值
  - bizuin: 显示实际值
  - slave_user: 显示实际值
  - slave_sid: 显示实际值

### 3. 数据库记录
所有相关的配置项都应该有值，不再是空字符串。

## 环境变量配置

为了确保修复生效，建议在部署环境中设置以下环境变量：

### Vercel 部署
```env
NEXTAUTH_URL=https://your-domain.vercel.app
```

### 自定义域名
```env
NEXTAUTH_URL=https://your-custom-domain.com
```

## 故障排除

### 1. 如果仍然保存失败

检查控制台日志中的错误信息：
- 确认API URL是否正确
- 检查网络连接
- 验证环境变量设置

### 2. 如果备用保存也失败

可能的原因：
- 数据库连接问题
- Prisma配置问题
- 权限问题

### 3. 调试步骤

1. 检查浏览器开发者工具的Network标签
2. 查看API调用是否成功
3. 检查服务器日志
4. 验证数据库连接

## 相关文件

修复涉及的文件：
- `lib/wechat-crawler-login.ts` - 主要修复文件
- `app/api/crawler/login/route.ts` - Cookie保存API
- `app/admin/crawler/page.tsx` - 管理界面
- `scripts/test-cookie-save-fix.js` - 测试脚本

## 后续改进建议

1. **监控和告警**: 添加Cookie保存失败的监控
2. **重试机制**: 实现自动重试机制
3. **健康检查**: 定期检查Cookie有效性
4. **用户通知**: 在界面上显示保存状态
