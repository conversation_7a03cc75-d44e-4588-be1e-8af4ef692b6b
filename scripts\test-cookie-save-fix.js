#!/usr/bin/env node

/**
 * 测试Cookie保存修复的脚本
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testCookieSaveFix() {
  console.log('🧪 测试Cookie保存修复...');
  
  try {
    // 创建微信登录实例
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成，长度:', qrcode.length);
      },
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        if (status === 'confirmed') {
          console.log('✅ 登录确认成功！');
          console.log('👤 用户信息:', data);
          
          // 测试Cookie保存
          const session = wechatLogin.getSession();
          if (session && session.cookies) {
            console.log('🍪 Cookie数量:', session.cookies.length);
            console.log('🔑 提取的Token:', session.extractedToken);
            
            // 检查Cookie是否包含关键字段
            const cookieString = session.cookies.join('; ');
            const hasDataTicket = cookieString.includes('data_ticket');
            const hasBizuin = cookieString.includes('bizuin');
            const hasRandInfo = cookieString.includes('rand_info');
            
            console.log('🔍 Cookie检查:');
            console.log('  data_ticket:', hasDataTicket ? '✅' : '❌');
            console.log('  bizuin:', hasBizuin ? '✅' : '❌');
            console.log('  rand_info:', hasRandInfo ? '✅' : '❌');
            
            if (hasDataTicket && hasBizuin && hasRandInfo) {
              console.log('✅ Cookie包含所有必要字段');
            } else {
              console.log('⚠️ Cookie缺少某些关键字段');
            }
          }
        }
      },
      onSuccess: async (userInfo, token) => {
        console.log('🎉 微信登录成功!');
        console.log('👤 用户信息:', userInfo);
        console.log('🔑 生成的Token:', token);
        
        // 验证Cookie是否已保存到数据库
        await verifyCookieInDatabase();
        
        process.exit(0);
      },
      onError: async (error) => {
        console.error('❌ 微信登录失败:', error);
        process.exit(1);
      }
    });

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('⏰ 等待扫码中...');
    
    // 设置超时
    setTimeout(() => {
      console.log('⏰ 测试超时，退出');
      wechatLogin.cancelLogin();
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyCookieInDatabase() {
  try {
    console.log('🔍 验证Cookie是否已保存到数据库...');
    
    const { prisma } = await import('../lib/prisma');
    
    // 检查关键配置是否存在（不再包含cookie_string）
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    console.log('📊 数据库中的配置数量:', configs.length);
    
    const configMap = {};
    configs.forEach(config => {
      configMap[config.name] = config.value;
    });

    // 检查每个配置
    for (const name of configNames) {
      const value = configMap[name];
      const status = value && value.length > 0 ? '✅' : '❌';
      const preview = value ? (value.length > 20 ? value.substring(0, 20) + '...' : value) : '未设置';
      console.log(`  ${name}: ${status} ${preview}`);
    }

    // 检查是否有有效的Cookie
    const hasValidCookie = !!(
      configMap.wechat_article_data_ticket &&
      configMap.wechat_article_token &&
      configMap.wechat_article_cookie_string
    );

    if (hasValidCookie) {
      console.log('✅ Cookie已成功保存到数据库');
      console.log('🕐 登录时间:', configMap.wechat_article_login_time);
    } else {
      console.log('❌ Cookie保存不完整或失败');
    }

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 验证数据库Cookie失败:', error);
  }
}

// 运行测试
testCookieSaveFix();
