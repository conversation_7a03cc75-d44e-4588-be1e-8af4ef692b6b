'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface BatchDownloadButtonProps {
  articleIds: string[];
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  onDownloadStart?: () => void;
  onDownloadComplete?: () => void;
}

export function BatchDownloadButton({ 
  articleIds, 
  className,
  variant = 'default',
  size = 'default',
  onDownloadStart,
  onDownloadComplete
}: BatchDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleBatchDownload = async () => {
    if (isDownloading || articleIds.length === 0) return;

    if (articleIds.length > 50) {
      toast.error('单次最多下载50篇文章');
      return;
    }

    setIsDownloading(true);
    onDownloadStart?.();
    
    try {
      const response = await fetch('/api/articles/download', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleIds,
          options: {
            withCredentials: true,
            timeout: 30
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '批量下载失败');
      }

      toast.success(`批量下载任务已创建，共 ${data.totalCount} 篇文章`);
      onDownloadComplete?.();
    } catch (error) {
      console.error('批量下载失败:', error);
      toast.error(error instanceof Error ? error.message : '批量下载失败');
    } finally {
      setIsDownloading(false);
    }
  };

  if (articleIds.length === 0) {
    return null;
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleBatchDownload}
      disabled={isDownloading}
      className={className}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      <span className="ml-2">
        {isDownloading 
          ? '批量下载中...' 
          : `批量下载 (${articleIds.length})`
        }
      </span>
    </Button>
  );
}
