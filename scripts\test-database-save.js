#!/usr/bin/env node

/**
 * 测试数据库保存功能
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testDatabaseSave() {
  console.log('🧪 测试数据库保存功能...');
  
  let saveSteps = {
    tokenSaveStarted: false,
    tokenSaveCompleted: false,
    cookieSaveStarted: false,
    cookieSaveCompleted: false,
    prismaImportSuccess: false,
    configSaveCount: 0
  };

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
      },
      
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        if (status === 'confirmed') {
          console.log('✅ 登录确认，即将开始保存流程');
        }
      },
      
      onSuccess: async (userInfo, token) => {
        console.log('🎉 登录成功！');
        console.log('👤 用户信息:', userInfo);
        console.log('🔑 Token:', token);
        
        // 等待一下确保所有保存操作完成
        setTimeout(async () => {
          console.log('\n📊 保存步骤检查:');
          console.log('='.repeat(50));
          console.log(`Token保存开始: ${saveSteps.tokenSaveStarted ? '✅' : '❌'}`);
          console.log(`Token保存完成: ${saveSteps.tokenSaveCompleted ? '✅' : '❌'}`);
          console.log(`Cookie保存开始: ${saveSteps.cookieSaveStarted ? '✅' : '❌'}`);
          console.log(`Cookie保存完成: ${saveSteps.cookieSaveCompleted ? '✅' : '❌'}`);
          console.log(`Prisma导入成功: ${saveSteps.prismaImportSuccess ? '✅' : '❌'}`);
          console.log(`配置保存数量: ${saveSteps.configSaveCount}`);
          
          // 验证数据库中的数据
          await verifyDatabaseData();
          
          process.exit(0);
        }, 3000);
      },
      
      onError: async (error) => {
        console.error('❌ 登录失败:', error);
        
        console.log('\n📊 错误时的保存步骤检查:');
        console.log('='.repeat(50));
        console.log(`Token保存开始: ${saveSteps.tokenSaveStarted ? '✅' : '❌'}`);
        console.log(`Token保存完成: ${saveSteps.tokenSaveCompleted ? '✅' : '❌'}`);
        console.log(`Cookie保存开始: ${saveSteps.cookieSaveStarted ? '✅' : '❌'}`);
        console.log(`Cookie保存完成: ${saveSteps.cookieSaveCompleted ? '✅' : '❌'}`);
        
        process.exit(1);
      },
      
      onCookieSave: async (cookies, token) => {
        console.log('🍪 Cookie保存回调触发');
        saveSteps.cookieSaveStarted = true;
        
        // 模拟保存过程监控
        console.log('💾 开始保存Cookie到数据库...');
        
        try {
          // 这里会调用实际的保存逻辑
          const { prisma } = await import('../lib/prisma');
          saveSteps.prismaImportSuccess = true;
          console.log('✅ Prisma导入成功');
          
          // 构建Cookie字符串
          const cookieString = Object.entries(cookies)
            .map(([key, value]) => `${key}=${value}`)
            .join(';');

          // 保存到数据库的配置项
          const configs = [
            { name: 'wechat_article_data_ticket', value: cookies.data_ticket || '' },
            { name: 'wechat_article_rand_info', value: cookies.rand_info || '' },
            { name: 'wechat_article_bizuin', value: cookies.bizuin || '' },
            { name: 'wechat_article_slave_sid', value: cookies.slave_sid || '' },
            { name: 'wechat_article_slave_user', value: cookies.slave_user || '' },
            { name: 'wechat_article_token', value: token || 'callback_save_token' },
            { name: 'wechat_article_cookie_string', value: cookieString },
            { name: 'wechat_article_session_id', value: 'test_session_' + Date.now() },
            { name: 'wechat_article_login_time', value: new Date().toISOString() }
          ];

          console.log('🔍 开始保存配置项，总数:', configs.length);

          // 使用upsert更新或创建配置
          for (let i = 0; i < configs.length; i++) {
            const config = configs[i];
            console.log(`🔍 保存配置 ${i + 1}/${configs.length}: ${config.name}`);
            
            await prisma.systemConfig.upsert({
              where: { name: config.name },
              update: {
                value: config.value,
                updatedAt: new Date()
              },
              create: {
                name: config.name,
                value: config.value
              }
            });
            
            saveSteps.configSaveCount++;
            console.log(`✅ 配置 ${config.name} 保存成功`);
          }

          await prisma.$disconnect();
          saveSteps.cookieSaveCompleted = true;
          console.log('✅ 微信登录Cookie已保存到数据库');
          
        } catch (error) {
          console.error('❌ Cookie保存失败:', error);
          throw error;
        }
      }
    });

    // 监控日志输出
    const originalConsoleLog = console.log;
    console.log = function(...args) {
      const message = args.join(' ');
      
      // 监控关键日志
      if (message.includes('保存token到数据库:')) {
        saveSteps.tokenSaveStarted = true;
      } else if (message.includes('token保存成功')) {
        saveSteps.tokenSaveCompleted = true;
      } else if (message.includes('保存Cookie到数据库...')) {
        saveSteps.cookieSaveStarted = true;
      } else if (message.includes('微信登录Cookie已保存到数据库')) {
        saveSteps.cookieSaveCompleted = true;
      }
      
      originalConsoleLog.apply(console, args);
    };

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('🔍 数据库保存监控已启用');
    
    // 设置超时
    setTimeout(() => {
      console.log('\n⏰ 测试超时');
      console.log('📊 保存步骤最终状态:');
      console.log(`Token保存开始: ${saveSteps.tokenSaveStarted ? '✅' : '❌'}`);
      console.log(`Token保存完成: ${saveSteps.tokenSaveCompleted ? '✅' : '❌'}`);
      console.log(`Cookie保存开始: ${saveSteps.cookieSaveStarted ? '✅' : '❌'}`);
      console.log(`Cookie保存完成: ${saveSteps.cookieSaveCompleted ? '✅' : '❌'}`);
      
      wechatLogin.cancelLogin();
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyDatabaseData() {
  try {
    console.log('\n🔍 验证数据库中的数据...');
    
    const { prisma } = await import('../lib/prisma');
    
    const configNames = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_cookie_string',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];

    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: configNames
        }
      }
    });

    console.log('📊 数据库验证结果:');
    console.log('='.repeat(50));
    
    let validCount = 0;
    for (const name of configNames) {
      const config = configs.find(c => c.name === name);
      const hasValue = config && config.value && config.value.length > 0;
      if (hasValue) validCount++;
      
      const status = hasValue ? '✅' : '❌';
      const preview = config?.value ? 
        (config.value.length > 30 ? config.value.substring(0, 30) + '...' : config.value) : 
        '未设置';
      const updateTime = config?.updatedAt ? config.updatedAt.toISOString() : '无';
      
      console.log(`${status} ${name}:`);
      console.log(`    值: ${preview}`);
      console.log(`    更新时间: ${updateTime}`);
    }

    console.log('='.repeat(50));
    console.log(`📊 配置完整度: ${validCount}/${configNames.length}`);

    if (validCount >= 6) {
      console.log('✅ 数据库保存验证通过');
    } else {
      console.log('❌ 数据库保存不完整');
    }

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 数据库验证失败:', error);
  }
}

// 运行测试
testDatabaseSave();
