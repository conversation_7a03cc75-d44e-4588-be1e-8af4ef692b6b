#!/usr/bin/env node

/**
 * 测试动态Cookie构建功能
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function testDynamicCookie() {
  console.log('🧪 测试动态Cookie构建功能...');
  
  try {
    // 1. 首先测试静态方法是否可以正常工作
    console.log('\n📊 测试1: 从数据库构建Cookie字符串');
    console.log('='.repeat(50));
    
    const cookieString = await WechatCrawlerLogin.buildCookieStringFromDatabase();
    console.log('🔍 构建的Cookie字符串:', cookieString);
    console.log('🔍 Cookie字符串长度:', cookieString.length);
    
    if (cookieString.length > 0) {
      console.log('✅ Cookie字符串构建成功');
      
      // 分析Cookie内容
      const cookiePairs = cookieString.split(';');
      console.log('🔍 Cookie字段数量:', cookiePairs.length);
      
      cookiePairs.forEach((pair, index) => {
        const [name, value] = pair.split('=');
        const preview = value && value.length > 20 ? value.substring(0, 20) + '...' : value;
        console.log(`  ${index + 1}. ${name}: ${preview}`);
      });
      
    } else {
      console.log('⚠️ Cookie字符串为空，可能数据库中没有Cookie数据');
    }
    
    // 2. 测试获取特定Cookie字段
    console.log('\n📊 测试2: 获取特定Cookie字段');
    console.log('='.repeat(50));
    
    const testFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
    
    for (const field of testFields) {
      const value = await WechatCrawlerLogin.getCookieField(field);
      const preview = value && value.length > 30 ? value.substring(0, 30) + '...' : value;
      const status = value ? '✅' : '❌';
      console.log(`${status} ${field}: ${preview || '未设置'}`);
    }
    
    // 3. 测试完整的登录流程（不保存cookie_string）
    console.log('\n📊 测试3: 完整登录流程（不保存cookie_string）');
    console.log('='.repeat(50));
    
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        
        // 等待3秒后取消，我们只是测试保存逻辑
        setTimeout(() => {
          console.log('🚫 测试完成，取消登录');
          wechatLogin.cancelLogin();
          
          // 验证保存后的数据
          setTimeout(async () => {
            await verifyDatabaseStructure();
            process.exit(0);
          }, 1000);
        }, 3000);
      },
      
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
      },
      
      onSuccess: async (userInfo, token) => {
        console.log('🎉 登录成功！');
        console.log('👤 用户信息:', userInfo);
        console.log('🔑 Token:', token);
        
        // 验证保存的数据结构
        await verifyDatabaseStructure();
        
        process.exit(0);
      },
      
      onError: async (error) => {
        console.log('❌ 登录错误:', error.message);
      },
      
      onCookieSave: async (cookies, token) => {
        console.log('🍪 Cookie保存回调触发');
        console.log('  Cookie字段数量:', Object.keys(cookies).length);
        console.log('  Token:', token ? `${token.substring(0, 8)}...` : 'none');
        
        // 检查保存的字段
        const criticalFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
        let validCount = 0;
        
        console.log('🔍 Cookie字段检查:');
        criticalFields.forEach(field => {
          const hasValue = cookies[field] && cookies[field].length > 0;
          if (hasValue) validCount++;
          console.log(`  ${field}: ${hasValue ? '✅' : '❌'}`);
        });
        
        console.log(`🎯 关键字段完整度: ${validCount}/${criticalFields.length}`);
        
        // 模拟保存过程
        console.log('💾 模拟保存Cookie到数据库（不包含cookie_string）...');
        
        try {
          const { prisma } = await import('../lib/prisma');
          
          const configs = [
            { name: 'wechat_article_data_ticket', value: cookies.data_ticket || '' },
            { name: 'wechat_article_rand_info', value: cookies.rand_info || '' },
            { name: 'wechat_article_bizuin', value: cookies.bizuin || '' },
            { name: 'wechat_article_slave_sid', value: cookies.slave_sid || '' },
            { name: 'wechat_article_slave_user', value: cookies.slave_user || '' },
            { name: 'wechat_article_token', value: token || 'test_token' },
            { name: 'wechat_article_session_id', value: 'test_session_' + Date.now() },
            { name: 'wechat_article_login_time', value: new Date().toISOString() }
          ];

          console.log('🔍 保存配置项数量:', configs.length);
          console.log('📝 注意: 不包含 wechat_article_cookie_string 字段');

          for (const config of configs) {
            await prisma.systemConfig.upsert({
              where: { name: config.name },
              update: {
                value: config.value,
                updatedAt: new Date()
              },
              create: {
                name: config.name,
                value: config.value
              }
            });
          }

          await prisma.$disconnect();
          console.log('✅ Cookie字段保存完成（不包含完整cookie_string）');
          
        } catch (error) {
          console.error('❌ Cookie保存失败:', error);
        }
      }
    });

    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 二维码已生成，3秒后将自动取消');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

async function verifyDatabaseStructure() {
  try {
    console.log('\n🔍 验证数据库结构...');
    console.log('='.repeat(50));
    
    const { prisma } = await import('../lib/prisma');
    
    // 获取所有微信相关配置
    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          startsWith: 'wechat_article_'
        }
      }
    });

    console.log('📊 数据库中的微信配置数量:', configs.length);
    
    const expectedFields = [
      'wechat_article_data_ticket',
      'wechat_article_rand_info',
      'wechat_article_bizuin',
      'wechat_article_slave_sid',
      'wechat_article_slave_user',
      'wechat_article_token',
      'wechat_article_session_id',
      'wechat_article_login_time'
    ];
    
    const unexpectedFields = ['wechat_article_cookie_string'];
    
    console.log('\n✅ 应该存在的字段:');
    expectedFields.forEach(field => {
      const config = configs.find(c => c.name === field);
      const exists = !!config;
      const preview = config?.value ? 
        (config.value.length > 30 ? config.value.substring(0, 30) + '...' : config.value) : 
        '未设置';
      console.log(`  ${exists ? '✅' : '❌'} ${field}: ${preview}`);
    });
    
    console.log('\n❌ 不应该存在的字段:');
    unexpectedFields.forEach(field => {
      const config = configs.find(c => c.name === field);
      const exists = !!config;
      console.log(`  ${exists ? '❌ 存在（应该删除）' : '✅ 不存在（正确）'} ${field}`);
    });
    
    // 测试动态构建Cookie字符串
    console.log('\n🔧 测试动态构建Cookie字符串:');
    const dynamicCookieString = await WechatCrawlerLogin.buildCookieStringFromDatabase();
    console.log(`🔍 动态构建的Cookie字符串长度: ${dynamicCookieString.length}`);
    
    if (dynamicCookieString.length > 0) {
      console.log('✅ 动态Cookie构建功能正常');
      
      // 显示构建的Cookie内容（脱敏）
      const cookiePairs = dynamicCookieString.split(';');
      console.log('🔍 构建的Cookie字段:');
      cookiePairs.forEach((pair, index) => {
        const [name] = pair.split('=');
        console.log(`  ${index + 1}. ${name}`);
      });
    } else {
      console.log('⚠️ 动态Cookie构建结果为空');
    }

    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 数据库结构验证失败:', error);
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

// 运行测试
testDynamicCookie();
