import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';
import { ArticleDownloader } from '../../../../lib/article-downloader';

const prisma = new PrismaClient();

// 下载单篇文章
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const { articleId, options = {} } = body;

    if (!articleId) {
      return NextResponse.json({ error: '文章ID不能为空' }, { status: 400 });
    }

    // 获取文章信息
    const article = await prisma.article.findUnique({
      where: { id: articleId },
      include: {
        wechatAccount: true
      }
    });

    if (!article) {
      return NextResponse.json({ error: '文章不存在' }, { status: 404 });
    }

    // 检查用户是否已订阅该公众号
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        wechatAccountId: article.wechatAccountId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: '您未订阅该公众号' }, { status: 403 });
    }

    // 检查是否已有下载记录
    const existingDownload = await prisma.articleDownload.findFirst({
      where: {
        articleId: articleId,
        userId: user.id,
        status: {
          in: ['COMPLETED', 'DOWNLOADING']
        }
      }
    });

    if (existingDownload) {
      if (existingDownload.status === 'DOWNLOADING') {
        return NextResponse.json({ 
          error: '文章正在下载中，请稍后查看' 
        }, { status: 409 });
      } else {
        return NextResponse.json({ 
          message: '文章已下载',
          download: existingDownload
        });
      }
    }

    // 创建下载记录
    const downloadRecord = await prisma.articleDownload.create({
      data: {
        articleId: articleId,
        userId: user.id,
        status: 'DOWNLOADING'
      }
    });

    // 异步下载文章
    const downloader = new ArticleDownloader();
    
    // 在后台执行下载
    downloader.downloadArticleHTML(article.url, article.title, options)
      .then(async ({ html, size }) => {
        // 保存HTML文件
        const filePath = await downloader['saveArticleHTML'](html, articleId, article.title);
        
        // 更新下载记录
        await prisma.articleDownload.update({
          where: { id: downloadRecord.id },
          data: {
            status: 'COMPLETED',
            filePath: filePath,
            fileSize: size,
            downloadTime: new Date()
          }
        });
        
        console.log(`✅ 文章下载完成: ${article.title}`);
      })
      .catch(async (error) => {
        // 更新下载记录为失败状态
        await prisma.articleDownload.update({
          where: { id: downloadRecord.id },
          data: {
            status: 'FAILED',
            errorMessage: error.message
          }
        });
        
        console.error(`❌ 文章下载失败: ${article.title}`, error);
      });

    return NextResponse.json({ 
      message: '下载任务已创建',
      downloadId: downloadRecord.id
    }, { status: 202 });

  } catch (error) {
    console.error('创建下载任务失败:', error);
    return NextResponse.json({ error: '创建下载任务失败' }, { status: 500 });
  }
}

// 批量下载文章
export async function PUT(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const { articleIds, options = {} } = body;

    if (!articleIds || !Array.isArray(articleIds) || articleIds.length === 0) {
      return NextResponse.json({ error: '文章ID列表不能为空' }, { status: 400 });
    }

    if (articleIds.length > 50) {
      return NextResponse.json({ error: '单次最多下载50篇文章' }, { status: 400 });
    }

    // 获取文章信息
    const articles = await prisma.article.findMany({
      where: { 
        id: { in: articleIds }
      },
      include: {
        wechatAccount: true
      }
    });

    if (articles.length === 0) {
      return NextResponse.json({ error: '未找到有效文章' }, { status: 404 });
    }

    // 检查用户订阅权限
    const wechatAccountIds = [...new Set(articles.map(a => a.wechatAccountId))];
    const subscriptions = await prisma.subscription.findMany({
      where: {
        userId: user.id,
        wechatAccountId: { in: wechatAccountIds },
        isActive: true
      }
    });

    const subscribedAccountIds = new Set(subscriptions.map(s => s.wechatAccountId));
    const validArticles = articles.filter(a => subscribedAccountIds.has(a.wechatAccountId));

    if (validArticles.length === 0) {
      return NextResponse.json({ error: '您未订阅这些文章的公众号' }, { status: 403 });
    }

    // 创建批量下载记录
    const downloadRecords = await Promise.all(
      validArticles.map(article => 
        prisma.articleDownload.create({
          data: {
            articleId: article.id,
            userId: user.id,
            status: 'PENDING'
          }
        })
      )
    );

    // 异步执行批量下载
    const downloader = new ArticleDownloader();
    const articleData = validArticles.map(a => ({
      id: a.id,
      url: a.url,
      title: a.title
    }));

    downloader.downloadArticles(articleData, user.id)
      .then(results => {
        console.log(`✅ 批量下载完成，成功: ${results.filter(r => r.success).length}/${results.length}`);
      })
      .catch(error => {
        console.error('❌ 批量下载失败:', error);
      });

    return NextResponse.json({ 
      message: '批量下载任务已创建',
      downloadIds: downloadRecords.map(r => r.id),
      totalCount: validArticles.length
    }, { status: 202 });

  } catch (error) {
    console.error('创建批量下载任务失败:', error);
    return NextResponse.json({ error: '创建批量下载任务失败' }, { status: 500 });
  }
}
