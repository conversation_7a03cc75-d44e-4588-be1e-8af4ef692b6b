import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../../lib/auth';

const prisma = new PrismaClient();

// 获取单个下载记录详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { id } = params;

    const download = await prisma.articleDownload.findFirst({
      where: {
        id,
        userId: user.id
      },
      include: {
        article: {
          include: {
            wechatAccount: {
              select: {
                id: true,
                name: true,
                avatar: true,
                description: true
              }
            }
          }
        },
        proxyServer: {
          select: {
            id: true,
            name: true,
            url: true
          }
        }
      }
    });

    if (!download) {
      return NextResponse.json({ error: '下载记录不存在' }, { status: 404 });
    }

    return NextResponse.json({ download });

  } catch (error) {
    console.error('获取下载记录详情失败:', error);
    return NextResponse.json({ error: '获取下载记录详情失败' }, { status: 500 });
  }
}

// 重新下载
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { id } = params;

    const download = await prisma.articleDownload.findFirst({
      where: {
        id,
        userId: user.id
      },
      include: {
        article: true
      }
    });

    if (!download) {
      return NextResponse.json({ error: '下载记录不存在' }, { status: 404 });
    }

    if (download.status === 'DOWNLOADING') {
      return NextResponse.json({ 
        error: '该文章正在下载中' 
      }, { status: 409 });
    }

    // 更新状态为下载中
    await prisma.articleDownload.update({
      where: { id },
      data: {
        status: 'DOWNLOADING',
        errorMessage: null,
        downloadTime: null
      }
    });

    // 异步重新下载
    const { ArticleDownloader } = await import('../../../../../lib/article-downloader');
    const downloader = new ArticleDownloader();
    
    downloader.downloadArticleHTML(download.article.url, download.article.title)
      .then(async ({ html, size }) => {
        // 保存HTML文件
        const filePath = await downloader['saveArticleHTML'](html, download.articleId, download.article.title);
        
        // 更新下载记录
        await prisma.articleDownload.update({
          where: { id },
          data: {
            status: 'COMPLETED',
            filePath: filePath,
            fileSize: size,
            downloadTime: new Date()
          }
        });
        
        console.log(`✅ 文章重新下载完成: ${download.article.title}`);
      })
      .catch(async (error) => {
        // 更新下载记录为失败状态
        await prisma.articleDownload.update({
          where: { id },
          data: {
            status: 'FAILED',
            errorMessage: error.message
          }
        });
        
        console.error(`❌ 文章重新下载失败: ${download.article.title}`, error);
      });

    return NextResponse.json({ 
      message: '重新下载任务已创建'
    }, { status: 202 });

  } catch (error) {
    console.error('重新下载失败:', error);
    return NextResponse.json({ error: '重新下载失败' }, { status: 500 });
  }
}

// 删除单个下载记录
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { id } = params;

    const download = await prisma.articleDownload.findFirst({
      where: {
        id,
        userId: user.id
      }
    });

    if (!download) {
      return NextResponse.json({ error: '下载记录不存在' }, { status: 404 });
    }

    if (download.status === 'DOWNLOADING') {
      return NextResponse.json({ 
        error: '无法删除正在下载的任务' 
      }, { status: 400 });
    }

    // 删除下载记录
    await prisma.articleDownload.delete({
      where: { id }
    });

    // TODO: 删除对应的文件
    // 这里应该实现文件删除逻辑

    return NextResponse.json({ message: '下载记录删除成功' });

  } catch (error) {
    console.error('删除下载记录失败:', error);
    return NextResponse.json({ error: '删除下载记录失败' }, { status: 500 });
  }
}
