#!/usr/bin/env node

/**
 * 调试最终登录请求问题
 */

const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login');

async function debugFinalLogin() {
  console.log('🔍 调试最终登录请求问题...');
  
  let debugInfo = {
    statusCheckCount: 0,
    lastStatusCheckTime: 0,
    finalLoginStartTime: 0,
    finalLoginEndTime: 0,
    errorOccurred: false,
    errorDetails: null
  };

  try {
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        console.log('🔍 调试: 二维码长度:', qrcode.length);
      },
      
      onStatusChange: async (status, data) => {
        debugInfo.statusCheckCount++;
        debugInfo.lastStatusCheckTime = Date.now();
        
        console.log(`📊 状态变化 #${debugInfo.statusCheckCount}: ${status}`);
        console.log('🔍 调试: 状态变化时间:', new Date().toISOString());
        
        if (status === 'scanned') {
          console.log('👀 已扫码，等待确认...');
          console.log('🔍 调试: 轮询频率将调整为0.5秒');
          
        } else if (status === 'confirmed') {
          console.log('✅ 已确认，即将开始最终登录...');
          debugInfo.finalLoginStartTime = Date.now();
          console.log('🔍 调试: 最终登录开始时间:', new Date(debugInfo.finalLoginStartTime).toISOString());
        }
      },
      
      onSuccess: async (userInfo, token) => {
        debugInfo.finalLoginEndTime = Date.now();
        
        console.log('\n🎉 登录成功！');
        console.log('='.repeat(50));
        
        const totalTime = debugInfo.finalLoginEndTime - debugInfo.finalLoginStartTime;
        console.log(`⏱️ 最终登录总耗时: ${totalTime}ms`);
        console.log(`📊 状态检查次数: ${debugInfo.statusCheckCount}`);
        console.log(`👤 用户信息:`, userInfo);
        console.log(`🔑 Token: ${token ? token.substring(0, 10) + '...' : 'none'}`);
        
        process.exit(0);
      },
      
      onError: async (error) => {
        debugInfo.errorOccurred = true;
        debugInfo.errorDetails = {
          message: error.message,
          stack: error.stack,
          time: new Date().toISOString()
        };
        
        console.error('\n❌ 登录失败！');
        console.error('='.repeat(50));
        console.error('错误信息:', error.message);
        console.error('错误时间:', debugInfo.errorDetails.time);
        console.error('状态检查次数:', debugInfo.statusCheckCount);
        
        if (debugInfo.finalLoginStartTime > 0) {
          const errorTime = Date.now() - debugInfo.finalLoginStartTime;
          console.error(`最终登录执行时间: ${errorTime}ms`);
        }
        
        console.error('完整错误:', error);
        process.exit(1);
      },
      
      onCookieSave: async (cookies, token) => {
        console.log('🍪 Cookie保存回调触发');
        console.log('  Cookie数量:', Object.keys(cookies).length);
        console.log('  Token:', token ? `${token.substring(0, 8)}...` : 'none');
        
        // 检查关键字段
        const criticalFields = ['data_ticket', 'rand_info', 'bizuin', 'slave_sid', 'slave_user'];
        let validCount = 0;
        
        console.log('🔍 Cookie字段检查:');
        criticalFields.forEach(field => {
          const hasValue = cookies[field] && cookies[field].length > 0;
          if (hasValue) validCount++;
          console.log(`  ${field}: ${hasValue ? '✅' : '❌'}`);
        });
        
        console.log(`🎯 关键字段完整度: ${validCount}/${criticalFields.length}`);
        
        // 模拟保存
        console.log('💾 模拟保存Cookie到数据库...');
        await new Promise(resolve => setTimeout(resolve, 100)); // 模拟异步保存
        console.log('✅ Cookie保存完成');
      }
    });

    // 开始登录流程
    console.log('🚀 开始微信登录流程...');
    const qrcode = await wechatLogin.startLogin();
    
    console.log('📱 请使用微信扫描二维码完成登录');
    console.log('🔍 调试模式已启用，将显示详细的执行信息');
    
    // 监控状态检查频率
    let lastCheckTime = Date.now();
    const checkMonitor = setInterval(() => {
      const now = Date.now();
      const timeSinceLastCheck = now - debugInfo.lastStatusCheckTime;
      
      if (timeSinceLastCheck > 5000 && debugInfo.statusCheckCount > 0) {
        console.log(`⚠️ 警告: 距离上次状态检查已过去 ${timeSinceLastCheck}ms`);
        console.log('🔍 可能的问题: 状态检查循环已停止');
        
        // 获取当前会话状态
        const session = wechatLogin.getSession();
        if (session) {
          console.log('📊 当前会话状态:', {
            id: session.id,
            status: session.status,
            cookieCount: session.cookies?.length || 0
          });
        }
      }
    }, 3000);
    
    // 设置超时
    setTimeout(() => {
      clearInterval(checkMonitor);
      
      console.log('\n⏰ 测试超时');
      console.log('='.repeat(50));
      console.log('📊 调试信息汇总:');
      console.log(`  状态检查次数: ${debugInfo.statusCheckCount}`);
      console.log(`  最后检查时间: ${debugInfo.lastStatusCheckTime > 0 ? new Date(debugInfo.lastStatusCheckTime).toISOString() : '无'}`);
      console.log(`  最终登录开始: ${debugInfo.finalLoginStartTime > 0 ? new Date(debugInfo.finalLoginStartTime).toISOString() : '未开始'}`);
      console.log(`  是否发生错误: ${debugInfo.errorOccurred}`);
      
      if (debugInfo.finalLoginStartTime > 0 && debugInfo.finalLoginEndTime === 0) {
        const stuckTime = Date.now() - debugInfo.finalLoginStartTime;
        console.log(`⚠️ 最终登录可能卡住了，已执行 ${stuckTime}ms`);
      }
      
      wechatLogin.cancelLogin();
      process.exit(0);
    }, 5 * 60 * 1000); // 5分钟超时
    
  } catch (error) {
    console.error('❌ 测试启动失败:', error);
    process.exit(1);
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

// 运行调试
debugFinalLogin();
