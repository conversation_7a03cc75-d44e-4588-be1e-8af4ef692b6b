#!/usr/bin/env node

/**
 * 数据库迁移脚本：增加Article表URL字段长度
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function migrateUrlLength() {
  console.log('🔄 开始数据库迁移：增加Article表URL字段长度...');
  
  try {
    // 1. 生成迁移文件
    console.log('📝 生成Prisma迁移文件...');
    execSync('npx prisma migrate dev --name increase-article-url-length', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('✅ 迁移文件生成成功');
    
    // 2. 验证迁移是否成功
    console.log('🔍 验证迁移结果...');
    
    const { prisma } = await import('../lib/prisma');
    
    // 测试插入一个长URL
    const testUrl = 'https://mp.weixin.qq.com/s?__biz=MjM5MjAxNDM4MA==&tempkey=MTMzMl9MbnZ3OFNzTmFxOExlTGZoVlVkTTdOWGpKZy10SU1XX29GVmdpR3gxaDNLd2RsN3drRjRjRkQ5SU11MGtUdHVfeTFKT3ZKU1FwdHRiTnFFcXUtTXQtUHFQQ1A5dFpfWWkzMjdUN3Q2V1p6cThjeWNJVVVNdzNLSUVlTG96RkpGWHRVOElma1E4SUVnVmRKbVpKVWFpN19PanpmNjJzQVVfRWdsYzVBfn4%3D&chksm=bdaef3538ad97a451188d665deec6d23b3a0d7f39babb890d8f1fe275737638fd16d08531141#rd';
    
    console.log('🧪 测试长URL插入...');
    console.log(`URL长度: ${testUrl.length} 字符`);
    
    // 查找一个测试用的微信公众号
    const testAccount = await prisma.wechatAccount.findFirst();
    
    if (!testAccount) {
      console.log('⚠️ 没有找到测试用的微信公众号，跳过URL长度测试');
    } else {
      try {
        // 尝试创建一个测试文章
        const testArticle = await prisma.article.create({
          data: {
            title: '测试长URL文章',
            url: testUrl,
            publishDate: new Date(),
            wechatAccountId: testAccount.id,
            summary: '这是一个用于测试长URL的文章'
          }
        });
        
        console.log('✅ 长URL插入成功，文章ID:', testArticle.id);
        
        // 验证URL是否完整保存
        const savedArticle = await prisma.article.findUnique({
          where: { id: testArticle.id }
        });
        
        if (savedArticle && savedArticle.url === testUrl) {
          console.log('✅ URL完整性验证通过');
        } else {
          console.log('❌ URL完整性验证失败');
          console.log('原始URL长度:', testUrl.length);
          console.log('保存URL长度:', savedArticle?.url?.length || 0);
        }
        
        // 清理测试数据
        await prisma.article.delete({
          where: { id: testArticle.id }
        });
        console.log('🧹 测试数据已清理');
        
      } catch (insertError) {
        console.error('❌ 长URL插入测试失败:', insertError);
        throw insertError;
      }
    }
    
    await prisma.$disconnect();
    
    console.log('\n🎉 数据库迁移完成！');
    console.log('📊 迁移摘要:');
    console.log('  - Article表URL字段已改为TEXT类型');
    console.log('  - 支持任意长度的URL（包括带参数的长URL）');
    console.log('  - 现有数据保持不变');
    console.log('  - 长URL插入测试通过');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    
    if (error.message.includes('migrate')) {
      console.log('\n💡 可能的解决方案:');
      console.log('1. 确保数据库连接正常');
      console.log('2. 检查Prisma配置是否正确');
      console.log('3. 手动运行: npx prisma migrate dev');
    }
    
    process.exit(1);
  }
}

// 检查是否在正确的目录中
if (!fs.existsSync('prisma/schema.prisma')) {
  console.error('❌ 错误: 请在项目根目录中运行此脚本');
  process.exit(1);
}

// 运行迁移
migrateUrlLength();
