{"name": "feedwe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && prisma migrate deploy && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "postdeploy": "node scripts/post-deploy-init.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "tsx scripts/seed.ts", "db:seed-more": "tsx scripts/seed-more.ts", "db:stats": "tsx scripts/data-stats.ts", "db:reset": "prisma migrate reset --force", "admin:create": "node scripts/create-admin.js", "migrate:prod": "node scripts/migrate-production.js", "build:no-migrate": "prisma generate && next build", "build:next": "next build", "build:windows": "node scripts/build-windows.js", "prisma:clean": "rimraf node_modules/.prisma && rimraf node_modules/@prisma/client", "prisma:reset-generate": "npm run prisma:clean && npm install && npx prisma generate", "vercel-build": "node scripts/vercel-build.js", "seed:accounts": "node scripts/seed-accounts.js", "seed:users": "node scripts/create-test-user.js", "test:webhook": "node scripts/test-webhook.js", "test:wechat-login": "node scripts/test-wechat-login.js", "test:full-crawler": "node scripts/test-full-crawler.js", "test:search-accounts": "node scripts/test-search-accounts.js", "test:login-save": "node scripts/test-login-with-cookie-save.js", "test:qr-display": "node scripts/test-qr-display.js", "test:mock-login": "node scripts/test-mock-login.js"}, "dependencies": {"@prisma/client": "^6.11.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "next": "15.3.5", "node-fetch": "^3.3.2", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "rimraf": "^6.0.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}