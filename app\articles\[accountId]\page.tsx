'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

type Article = {
  id: string;
  title: string;
  summary?: string;
  publishDate: string;
  readCount?: number;
  likeCount?: number;
  coverImage?: string;
};

type WechatAccount = {
  id: string;
  name: string;
  avatar: string;
  description?: string;
};

export default function ArticlesPage() {
  const params = useParams();
  const accountId = params.accountId as string;
  
  const [articles, setArticles] = useState<Article[]>([]);
  const [account, setAccount] = useState<WechatAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // 获取公众号信息和文章列表
  useEffect(() => {
    if (accountId) {
      fetchAccountInfo();
      fetchArticles(1);
    }
  }, [accountId]);

  const fetchAccountInfo = async () => {
    try {
      const res = await fetch(`/api/accounts/${accountId}`);
      const data = await res.json();
      setAccount(data);
    } catch (error) {
      console.error('获取公众号信息失败:', error);
    }
  };

  const fetchArticles = async (pageNum: number, append = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);

      const res = await fetch(`/api/articles?accountId=${accountId}&page=${pageNum}&limit=10`);
      const data = await res.json();
      
      if (append) {
        setArticles(prev => [...prev, ...data.articles]);
      } else {
        setArticles(data.articles);
      }
      
      setHasMore(data.hasMore);
      setPage(pageNum);
    } catch (error) {
      console.error('获取文章列表失败:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchArticles(page + 1, true);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-10">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 公众号信息头部 */}
      {account && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center mb-4">
            <img 
              src={account.avatar} 
              alt={account.name}
              className="w-16 h-16 rounded-full mr-4"
            />
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {account.name}
              </h1>
              {account.description && (
                <p className="text-gray-600">{account.description}</p>
              )}
            </div>
            <Link 
              href="/subscriptions" 
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              返回订阅
            </Link>
          </div>
        </div>
      )}

      {/* 文章列表 */}
      {articles.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">暂无文章</div>
        </div>
      ) : (
        <div className="space-y-6">
          {articles.map((article) => (
            <div 
              key={article.id} 
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
            >
              <Link href={`/articles/${accountId}/${article.id}`}>
                <div className="p-6">
                  <div className="flex">
                    {article.coverImage && (
                      <img 
                        src={article.coverImage} 
                        alt={article.title}
                        className="w-24 h-24 object-cover rounded-lg mr-4 flex-shrink-0"
                      />
                    )}
                    <div className="flex-1">
                      <h2 className="text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
                        {article.title}
                      </h2>
                      {article.summary && (
                        <p className="text-gray-600 mb-3 line-clamp-2">
                          {article.summary}
                        </p>
                      )}
                      <div className="flex items-center text-sm text-gray-500 space-x-4">
                        <span>{formatDate(article.publishDate)}</span>
                        {article.readCount !== undefined && (
                          <span>阅读 {article.readCount}</span>
                        )}
                        {article.likeCount !== undefined && (
                          <span>点赞 {article.likeCount}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}

          {/* 加载更多按钮 */}
          {hasMore && (
            <div className="text-center py-6">
              <button
                onClick={loadMore}
                disabled={loadingMore}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50"
              >
                {loadingMore ? '加载中...' : '加载更多'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
