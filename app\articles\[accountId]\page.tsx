'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { DownloadButton } from '../../../components/DownloadButton';
import { BatchDownloadButton } from '../../../components/BatchDownloadButton';

type Article = {
  id: string;
  title: string;
  summary?: string;
  publishDate: string;
  readCount?: number;
  likeCount?: number;
  coverImage?: string;
};

type WechatAccount = {
  id: string;
  name: string;
  avatar: string;
  description?: string;
};

export default function ArticlesPage() {
  const params = useParams();
  const accountId = params.accountId as string;
  
  const [articles, setArticles] = useState<Article[]>([]);
  const [account, setAccount] = useState<WechatAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [exporting, setExporting] = useState(false);

  // 获取公众号信息和文章列表
  useEffect(() => {
    if (accountId) {
      fetchAccountInfo();
      fetchArticles(1);
    }
  }, [accountId]);

  const fetchAccountInfo = async () => {
    try {
      const res = await fetch(`/api/accounts/${accountId}`);
      const data = await res.json();
      setAccount(data);
    } catch (error) {
      console.error('获取公众号信息失败:', error);
    }
  };

  const fetchArticles = async (pageNum: number, append = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);

      const res = await fetch(`/api/articles?accountId=${accountId}&page=${pageNum}&limit=10`);
      const data = await res.json();
      
      if (append) {
        setArticles(prev => [...prev, ...data.articles]);
      } else {
        setArticles(data.articles);
      }
      
      setHasMore(data.hasMore);
      setPage(pageNum);
    } catch (error) {
      console.error('获取文章列表失败:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchArticles(page + 1, true);
    }
  };

  // 选择相关函数
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedArticles([]);
  };

  const toggleArticleSelection = (articleId: string) => {
    setSelectedArticles(prev =>
      prev.includes(articleId)
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    );
  };

  const selectAllArticles = () => {
    setSelectedArticles(articles.map(article => article.id));
  };

  const clearSelection = () => {
    setSelectedArticles([]);
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedArticles.length === 0) {
      alert('请先选择要导出的文章');
      return;
    }

    setExporting(true);
    try {
      const selectedArticleData = articles.filter(article =>
        selectedArticles.includes(article.id)
      );

      const response = await fetch('/api/articles/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articles: selectedArticleData.map(article => ({
            title: article.title,
            url: article.url,
            publishDate: article.publishDate,
            summary: article.summary || '',
            readCount: article.readCount || 0,
            likeCount: article.likeCount || 0
          }))
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${account?.name || '文章'}_${new Date().toISOString().slice(0, 10)}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        setIsSelectionMode(false);
        setSelectedArticles([]);
      } else {
        alert('导出失败，请重试');
      }
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请重试');
    } finally {
      setExporting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-10">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 公众号信息头部 */}
      {account && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center mb-4">
            <img 
              src={account.avatar} 
              alt={account.name}
              className="w-16 h-16 rounded-full mr-4"
            />
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {account.name}
              </h1>
              {account.description && (
                <p className="text-gray-600">{account.description}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleSelectionMode}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  isSelectionMode
                    ? 'bg-red-500 hover:bg-red-600 text-white'
                    : 'bg-gray-500 hover:bg-gray-600 text-white'
                }`}
              >
                {isSelectionMode ? '取消选择' : '批量操作'}
              </button>
              <Link
                href="/subscriptions"
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                返回订阅
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* 批量操作控制栏 */}
      {isSelectionMode && (
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                已选择 {selectedArticles.length} 篇文章
              </span>
              <button
                onClick={selectAllArticles}
                className="text-blue-500 hover:text-blue-600 text-sm"
              >
                全选
              </button>
              <button
                onClick={clearSelection}
                className="text-gray-500 hover:text-gray-600 text-sm"
              >
                清空
              </button>
            </div>
            <div className="flex items-center space-x-2">
              {selectedArticles.length > 0 && (
                <>
                  <BatchDownloadButton
                    articleIds={selectedArticles}
                    onDownloadComplete={() => {
                      setIsSelectionMode(false);
                      setSelectedArticles([]);
                    }}
                  />
                  <button
                    onClick={handleExportExcel}
                    disabled={exporting}
                    className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                  >
                    {exporting ? '导出中...' : '导出Excel'}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 文章列表 */}
      {articles.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">暂无文章</div>
        </div>
      ) : (
        <div className="space-y-6">
          {articles.map((article) => (
            <div
              key={article.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
            >
              <div className="p-6">
                <div className="flex">
                  {/* 选择框 */}
                  {isSelectionMode && (
                    <div className="mr-4 flex items-start pt-2">
                      <input
                        type="checkbox"
                        checked={selectedArticles.includes(article.id)}
                        onChange={() => toggleArticleSelection(article.id)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>
                  )}

                  {/* 文章封面 */}
                  {article.coverImage && (
                    <img
                      src={article.coverImage}
                      alt={article.title}
                      className="w-24 h-24 object-cover rounded-lg mr-4 flex-shrink-0"
                    />
                  )}

                  {/* 文章内容 */}
                  <div className="flex-1">
                    <Link href={`/articles/${accountId}/${article.id}`}>
                      <h2 className="text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
                        {article.title}
                      </h2>
                    </Link>
                    {article.summary && (
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {article.summary}
                      </p>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-gray-500 space-x-4">
                        <span>{formatDate(article.publishDate)}</span>
                        {article.readCount !== undefined && (
                          <span>阅读 {article.readCount}</span>
                        )}
                        {article.likeCount !== undefined && (
                          <span>点赞 {article.likeCount}</span>
                        )}
                      </div>

                      {/* 操作按钮 */}
                      {!isSelectionMode && (
                        <div className="flex items-center space-x-2">
                          <DownloadButton
                            articleId={article.id}
                            articleTitle={article.title}
                            variant="outline"
                            size="sm"
                          />
                          <a
                            href={article.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:text-blue-600 text-sm"
                          >
                            查看原文
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* 加载更多按钮 */}
          {hasMore && (
            <div className="text-center py-6">
              <button
                onClick={loadMore}
                disabled={loadingMore}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50"
              >
                {loadingMore ? '加载中...' : '加载更多'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
