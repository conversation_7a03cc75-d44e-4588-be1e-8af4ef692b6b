/**
 * 测试Vercel Cron端点
 */

const https = require('https');
const http = require('http');

// 配置
const BASE_URL = process.env.APP_URL || 'http://localhost:3000';
const CRON_SECRET = process.env.CRON_SECRET;

console.log('🧪 测试Vercel Cron端点...\n');
console.log(`📡 目标URL: ${BASE_URL}`);

/**
 * 发送HTTP请求
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const requestOptions = {
      method: 'GET',
      timeout: 30000,
      headers: {
        'User-Agent': 'FeedWe-Cron-Test/1.0',
        ...options.headers
      }
    };

    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

/**
 * 测试应用状态
 */
async function testStatus() {
  console.log('1️⃣ 测试应用状态...');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/status`);
    
    if (response.statusCode === 200) {
      console.log('✅ 应用状态正常');
      console.log('📊 环境信息:');
      console.log(`  - Vercel环境: ${response.data.environment?.isVercel ? '是' : '否'}`);
      console.log(`  - 生产环境: ${response.data.environment?.isProduction ? '是' : '否'}`);
      console.log(`  - 定时任务禁用: ${response.data.scheduler?.disabled ? '是' : '否'}`);
      console.log(`  - Cron Jobs可用: ${response.data.cronJobs?.available ? '是' : '否'}`);
      
      if (response.data.scheduler?.status) {
        console.log('📋 定时任务状态:');
        console.log(`  - 运行中: ${response.data.scheduler.status.isRunning ? '是' : '否'}`);
      }
      
      return response.data;
    } else {
      console.log(`❌ 应用状态异常，状态码: ${response.statusCode}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ 获取应用状态失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试Cron端点
 */
async function testCronEndpoint() {
  console.log('\n2️⃣ 测试Cron端点...');
  
  try {
    const headers = {};
    if (CRON_SECRET) {
      headers['Authorization'] = `Bearer ${CRON_SECRET}`;
      console.log('🔐 使用Cron密钥进行身份验证');
    }
    
    console.log('⏳ 执行Cron任务（可能需要较长时间）...');
    const startTime = Date.now();
    
    const response = await makeRequest(`${BASE_URL}/api/cron/crawl-articles`, { headers });
    
    const duration = Date.now() - startTime;
    console.log(`⏱️ 执行耗时: ${duration}ms`);
    
    if (response.statusCode === 200) {
      console.log('✅ Cron任务执行成功');
      console.log('📊 执行结果:');
      console.log(`  - 新增文章: ${response.data.totalNewArticles || 0} 篇`);
      console.log(`  - 处理公众号: ${response.data.processedAccounts || 0} 个`);
      
      if (response.data.results && response.data.results.length > 0) {
        console.log('📋 详细结果:');
        response.data.results.forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.accountName}: ${result.newArticles} 篇新文章`);
          if (result.error) {
            console.log(`     ❌ 错误: ${result.error}`);
          }
        });
      }
      
      return true;
    } else if (response.statusCode === 401) {
      console.log('❌ Cron任务认证失败，请检查CRON_SECRET');
      return false;
    } else {
      console.log(`❌ Cron任务执行失败，状态码: ${response.statusCode}`);
      console.log('📄 错误信息:', response.data);
      return false;
    }
  } catch (error) {
    console.log(`❌ Cron端点测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试初始化端点
 */
async function testInitEndpoint() {
  console.log('\n3️⃣ 测试初始化端点...');
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/init`);
    
    if (response.statusCode === 200) {
      console.log('✅ 初始化端点正常');
      console.log('📊 初始化状态:');
      console.log(`  - 自动初始化: ${response.data.autoInitialized ? '已完成' : '未完成'}`);
      
      if (response.data.schedulerStatus) {
        console.log(`  - 定时任务运行: ${response.data.schedulerStatus.isRunning ? '是' : '否'}`);
      }
      
      return true;
    } else {
      console.log(`❌ 初始化端点异常，状态码: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 初始化端点测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  try {
    // 1. 测试应用状态
    const statusData = await testStatus();
    
    // 2. 测试Cron端点
    const cronSuccess = await testCronEndpoint();
    
    // 3. 测试初始化端点
    const initSuccess = await testInitEndpoint();
    
    // 4. 总结
    console.log('\n🏁 测试总结:');
    console.log(`📊 应用状态: ${statusData ? '✅ 正常' : '❌ 异常'}`);
    console.log(`🕐 Cron端点: ${cronSuccess ? '✅ 正常' : '❌ 异常'}`);
    console.log(`🚀 初始化端点: ${initSuccess ? '✅ 正常' : '❌ 异常'}`);
    
    if (statusData?.environment?.isVercel) {
      console.log('\n💡 Vercel环境提示:');
      console.log('- 定时任务已自动禁用，使用Cron Jobs代替');
      console.log('- Cron Jobs会每2分钟自动执行一次');
      console.log('- 可以在Vercel控制台查看Functions日志');
    } else {
      console.log('\n💡 本地环境提示:');
      console.log('- 可以使用内置定时任务');
      console.log('- Cron端点也可以手动调用进行测试');
    }
    
    const allSuccess = statusData && cronSuccess && initSuccess;
    process.exit(allSuccess ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testStatus, testCronEndpoint, testInitEndpoint };
